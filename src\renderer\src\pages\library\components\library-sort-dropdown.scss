@use "../../../scss/globals.scss";

/**
 * LibrarySortDropdown Styles
 *
 * Touch-optimized sort dropdown for library games.
 * Provides comprehensive sorting options with clear visual hierarchy.
 *
 * Key Features:
 * - Touch-friendly dropdown controls (48px+ minimum)
 * - Steam Deck optimizations (52px+ targets)
 * - Clear visual feedback for selected options
 * - Responsive layout for different screen sizes
 * - Accessible keyboard navigation
 * - Smooth animations and transitions
 */

.library-sort-dropdown {
  position: relative;
  display: inline-block;

  // Trigger Button - Touch Optimized
  &__trigger {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 14px;
    padding: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 2);
    color: globals.$muted-color;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    min-width: 200px;
    min-height: 48px; // Touch target minimum
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
      border-color: rgba(255, 255, 255, 0.15);
      transform: translateY(-2px);
      box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
    }

    &:focus {
      outline: 2px solid rgba(22, 177, 149, 0.4);
      outline-offset: 2px;
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    &[aria-expanded="true"] {
      background: linear-gradient(135deg, rgba(22, 177, 149, 0.1) 0%, rgba(22, 177, 149, 0.06) 100%);
      border-color: rgba(22, 177, 149, 0.2);
      box-shadow: 0 6px 24px rgba(22, 177, 149, 0.15);
      color: globals.$brand-teal;

      .library-sort-dropdown__trigger-arrow {
        transform: rotate(180deg);
      }
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-height: 52px;
      padding: calc(globals.$spacing-unit * 1.75) calc(globals.$spacing-unit * 2.25);
      min-width: 220px;
      border-radius: 16px;
    }

    // Touch feedback for mobile devices
    @media (hover: none) and (pointer: coarse) {
      &:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
      }
    }
  }

  &__trigger-content {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 0.75);
    width: 100%;
  }

  &__trigger-text {
    flex: 1;
    font-size: 16px;
    font-weight: 600;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      font-size: 18px;
    }

    @media (max-width: 768px) {
      font-size: 15px;
    }
  }

  &__trigger-arrow {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0.7;
    flex-shrink: 0;

    &--open {
      transform: rotate(180deg);
    }
  }

  &__menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: calc(globals.$spacing-unit * 0.5);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(0, 0, 0, 0.9) 100%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    backdrop-filter: blur(24px);
    box-shadow:
      0 12px 48px rgba(0, 0, 0, 0.6),
      0 6px 24px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    z-index: 9999;
    animation: menuSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    min-width: 220px;
    max-width: 300px; // Prevent menu from being too wide

    // Position variants
    &--left {
      left: 0;
      right: auto;
    }

    &--right {
      left: auto;
      right: 0;
    }

    // Ensure menu doesn't go off-screen on mobile
    @media (max-width: 768px) {
      min-width: 200px;
      max-width: 250px;
    }
  }

  &__menu-header {
    padding: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
  }

  &__menu-title {
    font-size: globals.$small-font-size;
    font-weight: 700;
    color: globals.$muted-color;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  &__menu-content {
    padding: calc(globals.$spacing-unit * 1);
    max-height: 300px;
    overflow-y: auto;
  }

  // Menu Options - Touch Optimized
  &__option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    background: transparent;
    border: none;
    padding: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 2);
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: left;
    border-radius: 12px;
    font-size: 15px;
    font-weight: 500;
    margin-bottom: calc(globals.$spacing-unit * 0.75);
    min-height: 48px; // Touch target minimum

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.06) 100%);
      color: globals.$muted-color;
      transform: translateX(4px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &:focus {
      outline: 2px solid rgba(22, 177, 149, 0.4);
      outline-offset: 2px;
    }

    &--selected {
      background: linear-gradient(135deg, rgba(22, 177, 149, 0.15) 0%, rgba(22, 177, 149, 0.1) 100%);
      border: 1px solid rgba(22, 177, 149, 0.3);
      color: globals.$brand-teal;

      &:hover {
        background: linear-gradient(135deg, rgba(22, 177, 149, 0.2) 0%, rgba(22, 177, 149, 0.15) 100%);
        color: globals.$brand-teal;
        border-color: rgba(22, 177, 149, 0.4);
      }
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-height: 52px;
      padding: calc(globals.$spacing-unit * 1.75) calc(globals.$spacing-unit * 2.25);
      font-size: 16px;
      border-radius: 14px;
    }

    // Touch feedback for mobile devices
    @media (hover: none) and (pointer: coarse) {
      &:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
      }
    }
  }

  &__option-content {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1);
    flex: 1;
    min-width: 0;

    svg {
      opacity: 0.7;
      transition: opacity 0.2s ease;
      flex-shrink: 0;
    }
  }

  &__option-text {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__option-check {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    flex-shrink: 0;
  }

  &__option-check-mark {
    width: 8px;
    height: 8px;
    background: rgba(62, 98, 192, 0.9);
    border-radius: 50%;
    box-shadow: 0 0 0 2px rgba(62, 98, 192, 0.3);
  }

  &__option:hover &__option-content svg {
    opacity: 1;
  }

  // Scrollbar styling for menu content
  &__menu-content::-webkit-scrollbar {
    width: 6px;
  }

  &__menu-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  &__menu-content::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.25);
    }
  }
}

@keyframes menuSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .library-sort-dropdown {
    &__trigger {
      min-width: 140px;
      padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit * 1);
    }

    &__trigger-text {
      font-size: globals.$small-font-size;
    }

    &__menu {
      min-width: 180px;
    }

    &__option {
      padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit * 1);
      font-size: globals.$small-font-size;
    }
  }
}
