{"language_name": "De<PERSON>ch", "app": {"successfully_signed_in": "Erfolgreich angemeldet"}, "home": {"featured": "<PERSON><PERSON><PERSON><PERSON>", "surprise_me": "Überrasche mich", "no_results": "<PERSON><PERSON> gefunden", "start_typing": "<PERSON><PERSON><PERSON>, um zu suchen...", "hot": "Jetzt beliebt", "weekly": "📅 Top-Spiele der Woche", "achievements": "🏆 Spiele zum Meistern"}, "sidebar": {"catalogue": "Katalog", "downloads": "Downloads", "library": "Bibliothek", "settings": "Einstellungen", "my_library": "Meine Bibliothek", "downloading_metadata": "{{title}} (Metadaten werden heruntergeladen…)", "paused": "{{title}} (<PERSON><PERSON><PERSON><PERSON>)", "downloading": "{{title}} ({{percentage}} - Wird <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…)", "filter": "Bibliothek filtern", "home": "Home", "queued": "{{title}} (In Warteschlange)", "game_has_no_executable": "Spiel hat keine ausführbare Datei gewählt", "sign_in": "Anmelden", "friends": "Freunde", "need_help": "<PERSON>rauchst du Hilfe?", "favorites": "<PERSON><PERSON>", "playable_button_title": "<PERSON><PERSON> <PERSON>piele anzeigen, die du jetzt spielen kannst"}, "header": {"search": "<PERSON><PERSON><PERSON> suchen", "home": "Home", "catalogue": "Katalog", "downloads": "Downloads", "search_results": "Suchergebnisse", "settings": "Einstellungen", "version_available_install": "Version {{version}} verfügbar. K<PERSON>e hier, um neuzustarten und sie zu installieren.", "version_available_download": "Version {{version}} verfügbar. <PERSON><PERSON><PERSON> hier, um sie herunterzuladen."}, "bottom_panel": {"no_downloads_in_progress": "Keine aktive Downloads", "downloading_metadata": "<PERSON><PERSON><PERSON> von {{title}} werden herunt<PERSON>n…", "downloading": "{{title}} wird her<PERSON><PERSON><PERSON><PERSON><PERSON>… ({{percentage}} abgeschlossen) - Abschluss {{eta}} - {{speed}}", "calculating_eta": "{{title}} wird herunt<PERSON><PERSON><PERSON>n… ({{percentage}} abgeschlossen) - Verbleibende Zeit wird berechnet…", "checking_files": "<PERSON><PERSON><PERSON><PERSON> von {{title}}… ({{percentage}} abgeschlossen)", "installing_common_redist": "{{log}}…", "installation_complete": "Installation abgeschlossen", "installation_complete_message": "Allgemeine Redistributables erfolgreich installiert"}, "catalogue": {"search": "Filtern…", "developers": "<PERSON><PERSON><PERSON><PERSON>", "genres": "Genres", "tags": "Tags", "publishers": "Publisher", "download_sources": "Download-Quellen", "result_count": "{{resultCount}} Ergebnisse", "filter_count": "{{filterCount}} verfügbar", "clear_filters": "{{filterCount}} ausgewählte löschen", "next_page": "Nächste Seite", "previous_page": "Vorherige Seite"}, "game_details": {"open_download_options": "Download-Optionen ö<PERSON>nen", "download_options_zero": "<PERSON><PERSON> Download-Optionen", "download_options_one": "{{count}} Download-Option", "download_options_other": "{{count}} Download-Optionen", "updated_at": "Aktualisiert {{updated_at}}", "install": "Installieren", "resume": "Fortfahren", "pause": "Pausieren", "cancel": "Abbrechen", "remove": "Entfernen", "space_left_on_disk": "{{space}} auf Festplatte verfügbar", "eta": "Abschluss {{eta}}", "calculating_eta": "Verbleibende Zeit wird berechnet…", "downloading_metadata": "Metadaten werden heruntergeladen…", "filter": "Repacks filtern", "requirements": "Systemanforderungen", "minimum": "Minimum", "recommended": "<PERSON><PERSON><PERSON><PERSON>", "paused": "<PERSON><PERSON><PERSON><PERSON>", "release_date": "Veröffentlicht am {{date}}", "publisher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von {{publisher}}", "hours": "Stunden", "minutes": "Minuten", "amount_hours": "{{amount}} St<PERSON><PERSON>", "amount_minutes": "{{amount}} Minuten", "accuracy": "{{accuracy}}% Genauigkeit", "add_to_library": "Zu Bibliothek hinzufügen", "remove_from_library": "Von Bibliothek entfernen", "no_downloads": "Keine Downloads verfügbar", "play_time": "{{amount}} lang gespielt", "last_time_played": "Zuletzt gespielt {{period}}", "not_played_yet": "{{title}} wurde noch nicht gespielt", "next_suggestion": "Nächste Empfehlung", "play": "<PERSON><PERSON><PERSON>", "deleting": "Installer wird gel<PERSON>t…", "close": "Schließen", "playing_now": "Spielt jetzt", "change": "Ändern", "repacks_modal_description": "<PERSON><PERSON><PERSON><PERSON> Repack, das du herunterladen möchtest", "select_folder_hint": "Um das Standardverzeichnis zu ändern, gehe zu den <0>Einstellungen</0>", "download_now": "<PERSON><PERSON><PERSON> herunterladen", "no_shop_details": "Shop-Details konnten nicht abgerufen werden.", "download_options": "Download-Optionen", "download_path": "Download-Verzeichnis", "previous_screenshot": "<PERSON><PERSON><PERSON><PERSON> Screenshot", "next_screenshot": "Nächster Screenshot", "screenshot": "Screenshot {{number}}", "open_screenshot": "Screenshot {{number}} <PERSON><PERSON><PERSON>", "download_settings": "Download-Einstellungen", "downloader": "Downloader", "select_executable": "Auswählen", "no_executable_selected": "<PERSON><PERSON> ausführbare Datei gewählt", "open_folder": "Verzeichnis öffnen", "open_download_location": "Heruntergeladene Date<PERSON> anzeigen", "create_shortcut": "Desktop-Verknüpfung erstellen", "clear": "Löschen", "remove_files": "<PERSON><PERSON>", "remove_from_library_title": "Bist du dir sicher?", "remove_from_library_description": "Dies wird {{game}} aus deiner Bibliothek entfernen", "options": "Optionen", "executable_section_title": "Ausführbar<PERSON>", "executable_section_description": "Pfad der Datei, die bei Klick auf \"Play\" ausgeführt wird", "downloads_section_title": "Downloads", "downloads_section_description": "<PERSON><PERSON> dir Updates oder andere Versionen dieses Spiels an", "danger_zone_section_title": "Gefahrenzone", "danger_zone_section_description": "<PERSON><PERSON><PERSON><PERSON> dieses Spiel aus deiner Bibliothek oder die von Hydra heruntergeladenen Dateien", "download_in_progress": "Download erfolgt", "download_paused": "Download ist pausiert", "last_downloaded_option": "Letzte Download-Option", "create_steam_shortcut": "Steam-Verknüpfung erstellen", "create_shortcut_success": "Verknüpfung erfolgreich erstellt", "you_might_need_to_restart_steam": "Möglicherweise musst du Steam neu starten, um die Änderungen zu sehen", "create_shortcut_error": "Fehler bei Erstellung von Verknüpfung", "nsfw_content_title": "<PERSON>ses <PERSON>l enthält unangemessene Inhalte", "nsfw_content_description": "{{title}} enth<PERSON><PERSON>, die möglicherweise nicht für alle Altersgruppen geeignet sind. B<PERSON> du sicher, dass du fortfahren möchtest?", "allow_nsfw_content": "Fortfahren", "refuse_nsfw_content": "Zurück", "stats": "Statistiken", "download_count": "Downloads", "player_count": "Aktive Spieler", "download_error": "Diese Download-Option ist nicht verfügbar", "download": "Download", "executable_path_in_use": "Ausführbare Datei wird bereits von \"{{game}}\" verwendet", "warning": "Warnung:", "hydra_needs_to_remain_open": "<PERSON><PERSON><PERSON> diesen Download muss Hydra geöffnet bleiben, bis er abgeschlossen ist. Wenn Hydra vor Abschluss geschlossen wird, verlierst du deinen Fortschritt.", "achievements": "Erfolge", "achievements_count": "Erfolge {{unlockedCount}}/{{achievementsCount}}", "cloud_save": "Cloud-Speicherstand", "cloud_save_description": "Speichere deinen Fortschritt in der Cloud und spiele auf jedem Gerät weiter", "backups": "Sicherungen", "install_backup": "Installieren", "delete_backup": "Löschen", "create_backup": "Neue Sicherung", "last_backup_date": "Letzte Sicherung am {{date}}", "no_backup_preview": "<PERSON>ine Spielstände für diesen Titel gefunden", "restoring_backup": "Sicherung wird wiederhergestellt ({{progress}} abgeschlossen)…", "uploading_backup": "Sicherung wird hochgeladen…", "no_backups": "Du hast noch keine Sicherungen für dieses Spiel erstellt", "backup_uploaded": "Sicherung hochgeladen", "backup_deleted": "Sicherung gelöscht", "backup_restored": "Sicherung wiederhergestellt", "see_all_achievements": "Alle Erfolge anzeigen", "sign_in_to_see_achievements": "An<PERSON><PERSON>, um Erfolge zu sehen", "mapping_method_automatic": "Automatisch", "mapping_method_manual": "<PERSON><PERSON>", "mapping_method_label": "Zuordnungsmethode", "files_automatically_mapped": "Dateien automatisch zugeordnet", "no_backups_created": "<PERSON>ine Sicherungen für dieses Spiel erstellt", "manage_files": "<PERSON><PERSON> ver<PERSON>", "loading_save_preview": "Suche nach Spielständen…", "wine_prefix": "Wine-Präfix", "wine_prefix_description": "Das Wine-Präfix, das zum Ausführen dieses Spiels verwendet wird", "launch_options": "Startoptionen", "launch_options_description": "Fortgeschrittene Benutzer können Modifikationen ihrer Startoptionen eingeben (experimentelle Funktion)", "launch_options_placeholder": "<PERSON><PERSON> angegeben", "no_download_option_info": "<PERSON><PERSON>en verfügbar", "backup_deletion_failed": "Sicherung konnte nicht gelöscht werden", "max_number_of_artifacts_reached": "Maximale Anzahl von Sicherungen für dieses Spiel erreicht", "achievements_not_sync": "<PERSON><PERSON>, wie du deine Erfolge synchronisieren kannst", "manage_files_description": "<PERSON><PERSON><PERSON><PERSON>, welche Dateien gesichert und wiederhergestellt werden", "select_folder": "Ordner auswählen", "backup_from": "Sicherung vom {{date}}", "automatic_backup_from": "Automatische Sicherung vom {{date}}", "enable_automatic_cloud_sync": "Automatische Cloud-Synchronisierung aktivieren", "custom_backup_location_set": "Benutzerdefinierter Sicherungsort festgelegt", "no_directory_selected": "<PERSON><PERSON> Verzeichnis ausgewählt", "no_write_permission": "Kann nicht in dieses Verzeichnis herunterladen. <PERSON><PERSON><PERSON> hier, um mehr zu erfahren.", "reset_achievements": "Erfolge zurücksetzen", "reset_achievements_description": "Dies wird alle Erfolge für {{game}} zurücksetzen", "reset_achievements_title": "Bist du dir sicher?", "reset_achievements_success": "Erfolge erfolgreich zurückgesetzt", "reset_achievements_error": "Fehler beim Zurücksetzen der Erfolge", "download_error_gofile_quota_exceeded": "Du hast dein monatliches Gofile-Kontingent überschritten. <PERSON>te warte, bis das Kontingent zurückgesetzt wird.", "download_error_real_debrid_account_not_authorized": "Dein Real-Debrid-Konto ist nicht für neue Downloads autorisiert. Bitte überprüfe deine Kontoeinstellungen und versuche es erneut.", "download_error_not_cached_on_real_debrid": "Dieser Download ist nicht auf Real-Debrid verfügbar und das Abrufen des Download-Status von Real-Debrid ist noch nicht verfügbar.", "download_error_not_cached_on_torbox": "Dieser Download ist nicht auf TorBox verfügbar und das Abrufen des Download-Status von TorBox ist noch nicht verfügbar.", "download_error_not_cached_on_hydra": "Dieser Download ist nicht auf Nimbus verfügbar.", "game_removed_from_favorites": "Spiel aus Favoriten entfernt", "game_added_to_favorites": "Spiel zu Favorite<PERSON> hinzugefügt", "automatically_extract_downloaded_files": "Heruntergeladene Dateien automatisch entpacken", "create_start_menu_shortcut": "Startmenü-Verknüpfung erstellen", "invalid_wine_prefix_path": "Ungültiger Wine-Präfix-Pfad", "invalid_wine_prefix_path_description": "Der Pfad zum Wine-Präfix ist ungültig. Bitte überprüfe den Pfad und versuche es erneut.", "missing_wine_prefix": "Wine-Präfix ist erford<PERSON>lich, um eine Sicherung unter Linux zu erstellen"}, "activation": {"title": "Hydra aktivieren", "installation_id": "Installations ID:", "enter_activation_code": "Aktivierungscode eingeben", "message": "Wenn du nicht weißt wo du fragen musst, solltest du dies nicht haben.", "activate": "Aktivieren", "loading": "<PERSON><PERSON><PERSON>…"}, "downloads": {"resume": "Fortfahren", "pause": "Pause", "eta": "Abschluss {{eta}}", "paused": "<PERSON><PERSON><PERSON><PERSON>", "verifying": "Verifiziere…", "completed": "Abgeschlossen", "removed": "<PERSON>cht heruntergeladen", "cancel": "Abbrechen", "filter": "Heruntergeladene Spiele filtern", "remove": "Entfernen", "downloading_metadata": "Metadaten werden heruntergeladen…", "deleting": "Installer wird entfernt…", "delete": "Installer entfernen", "delete_modal_title": "Bist du dir sicher?", "delete_modal_description": "Dies wird alle Installationsdateien von deinem Computer entfernen", "install": "Installieren", "download_in_progress": "Läuft", "queued_downloads": "Downloads in Warteschlange", "downloads_completed": "Abgeschlossen", "queued": "In Warteschlange", "no_downloads_title": "<PERSON>", "no_downloads_description": "Du hast mit Hydra noch nichts heruntergel<PERSON>n, aber es ist nie zu spät anzufangen.", "checking_files": "Dateien werden überprüft…", "seeding": "Seeding", "stop_seeding": "Seeding stoppen", "resume_seeding": "Seeding fortsetzen", "options": "<PERSON><PERSON><PERSON><PERSON>", "extract": "<PERSON><PERSON> en<PERSON>", "extracting": "<PERSON><PERSON> werden entpackt…"}, "settings": {"downloads_path": "Download-Pfad", "change": "Aktualisieren", "notifications": "Benachrichtigungen", "enable_download_notifications": "Wenn ein Download abgeschlossen wird", "enable_repack_list_notifications": "Wenn ein neues Repack hinzugefügt wird", "real_debrid_api_token_label": "Real-Debrid API Token", "quit_app_instead_hiding": "Hydra verlassen statt minimieren beim Schließen", "launch_with_system": "Hydra bei Systemstart starten", "general": "Allgemein", "behavior": "Verhalten", "download_sources": "Download-Quellen", "language": "<PERSON><PERSON><PERSON>", "api_token": "API Token", "enable_real_debrid": "Real-Debrid aktivieren", "real_debrid_description": "Real-Debrid ist ein unrestriktiver Downloader, der es dir ermöglicht Dateien sofort und mit deiner maximalen Internetgeschwindigkeit herunterzuladen.", "debrid_invalid_token": "API token nicht gültig", "debrid_api_token_hint": "<0>Hier</0> kannst du dir deinen API Token holen", "real_debrid_free_account_error": "Das Konto \"{{username}}\" ist ein gratis account. Bitte abonniere Real-Debrid", "debrid_linked_message": "<PERSON><PERSON> \"{{username}}\" verknüpft", "save_changes": "Änderungen speichern", "changes_saved": "Änderungen erfolgreich gespeichert", "download_sources_description": "Hydra wird die Download-Links von diesen Quellen abrufen. Die Quell-URL muss ein direkter Link zu einer .<PERSON><PERSON>, welche die Download-<PERSON>s enthält, sein.", "validate_download_source": "Validieren", "remove_download_source": "Entfernen", "add_download_source": "<PERSON><PERSON>", "download_count_zero": "Keine Download-Option", "download_count_one": "{{countFormatted}} Download-Option", "download_count_other": "{{countFormatted}} Download-Optionen", "download_source_url": "Download Quell-URL", "add_download_source_description": "<PERSON>üge die URL, welche die .json Datei enthält, ein", "download_source_up_to_date": "Auf aktuellem Stand", "download_source_errored": "Fehlgeschlagen", "sync_download_sources": "<PERSON><PERSON> synchronisieren", "removed_download_source": "Download-<PERSON><PERSON> en<PERSON>nt", "removed_download_sources": "Download-<PERSON><PERSON> entfernt", "cancel_button_confirmation_delete_all_sources": "<PERSON><PERSON>", "confirm_button_confirmation_delete_all_sources": "<PERSON><PERSON>, alles löschen", "title_confirmation_delete_all_sources": "Möchtest du alle Downloadquellen löschen", "description_confirmation_delete_all_sources": "Möchtest du alle Downloadquellen löschen", "button_delete_all_sources": "Entfernen Sie alle Downloadquellen", "added_download_source": "Download-<PERSON><PERSON>", "download_sources_synced": "Alle Download-<PERSON><PERSON> sind synchronisiert", "insert_valid_json_url": "Füge eine gültige JSON URL ein", "found_download_option_zero": "<PERSON><PERSON> Download-Option gefunden", "found_download_option_one": "{{countFormatted}} Download-Option gefunden", "found_download_option_other": "{{countFormatted}} Download-Optionen gefunden", "import": "Importieren", "public": "<PERSON><PERSON><PERSON><PERSON>", "private": "Privat", "friends_only": "Nur Freunde", "privacy": "Privatsphäre", "profile_visibility": "Profilsichtbarkeit", "profile_visibility_description": "<PERSON><PERSON><PERSON><PERSON>, wer dein Profil und deine Bibliothek sehen kann", "required_field": "<PERSON><PERSON> ist erford<PERSON>lich", "source_already_exists": "<PERSON><PERSON> wurde bereits hinzugefügt", "must_be_valid_url": "Die Quelle muss eine gültige URL sein", "blocked_users": "<PERSON><PERSON><PERSON>", "user_unblocked": "Benutzer wurde freigegeben", "enable_achievement_notifications": "Wenn ein Erfolg freigeschaltet wird", "launch_minimized": "Hydra minimiert starten", "disable_nsfw_alert": "NSFW-War<PERSON>ng deaktivieren", "seed_after_download_complete": "Nach Download-<PERSON><PERSON>ch<PERSON><PERSON> seeden", "show_hidden_achievement_description": "Versteckte Erfolgsbeschreibungen vor dem Freischalten anzeigen", "account": "Ko<PERSON>", "no_users_blocked": "Du hast keine blockier<PERSON>utzer", "subscription_active_until": "Deine Hydra Cloud ist aktiv bis {{date}}", "manage_subscription": "Abonnement verwalten", "update_email": "E-Mail aktualisieren", "update_password": "Passwort aktualisieren", "current_email": "Aktuelle E-Mail:", "no_email_account": "Du hast noch keine E-Mail festgelegt", "account_data_updated_successfully": "Kontodaten erfolgreich aktualisiert", "renew_subscription": "Hydra Cloud <PERSON>n", "subscription_expired_at": "Dein Abonnement ist am {{date}} abgelaufen", "no_subscription": "Genieße Hydra auf die bestmögliche Weise", "become_subscriber": "Werde Hydra Cloud", "subscription_renew_cancelled": "Automatische Verlängerung ist deaktiviert", "subscription_renews_on": "Dein Abonnement verlängert sich am {{date}}", "bill_sent_until": "Deine nächste Rechnung wird bis zu diesem Tag gesendet", "no_themes": "<PERSON><PERSON><PERSON>, als hättest du noch keine Themes, aber keine Sorge, klicke hier, um dein erstes Meisterwerk zu erstellen.", "editor_tab_code": "Code", "editor_tab_info": "Info", "editor_tab_save": "Speichern", "web_store": "Web Store", "clear_themes": "Löschen", "create_theme": "<PERSON><PERSON><PERSON><PERSON>", "create_theme_modal_title": "Benutzerdefiniertes Theme erstellen", "create_theme_modal_description": "<PERSON><PERSON><PERSON> ein neues Theme, um das Aussehen von Hydra anzupassen", "theme_name": "Name", "insert_theme_name": "Theme-<PERSON><PERSON> e<PERSON>", "set_theme": "Theme festlegen", "unset_theme": "Theme entfernen", "delete_theme": "Theme löschen", "edit_theme": "Theme bearbeiten", "delete_all_themes": "Alle Themes löschen", "delete_all_themes_description": "Dies wird alle deine benutzerdefinierten Themes löschen", "delete_theme_description": "Dies wird das Theme {{theme}} löschen", "cancel": "Abbrechen", "appearance": "Erscheinungsbild", "enable_torbox": "TorBox aktivieren", "torbox_description": "TorBox ist dein Premium-Seedbox-Service, der sogar mit den besten Servern auf dem Markt konkurriert.", "torbox_account_linked": "TorBox-Konto verknüpft", "create_real_debrid_account": "<PERSON><PERSON><PERSON> hier, wenn du noch kein Real-Debrid-<PERSON><PERSON> hast", "create_torbox_account": "<PERSON><PERSON><PERSON> hier, wenn du noch kein TorBox-<PERSON><PERSON> hast", "real_debrid_account_linked": "Real-Debrid-Konto verknüpft", "name_min_length": "Theme-Name muss mind<PERSON> 3 <PERSON><PERSON><PERSON> lang sein", "import_theme": "Theme importieren", "import_theme_description": "Du wirst {{theme}} aus dem Theme Store importieren", "error_importing_theme": "Fehler beim Importieren des Themes", "theme_imported": "Theme erfolgreich importiert", "enable_friend_request_notifications": "Wenn eine Freundschaftsanfrage empfangen wird", "enable_auto_install": "Updates automatisch herunterladen", "common_redist": "Allgemeine Redistributables", "common_redist_description": "Allgemeine Redistributables sind erforderlich, um einige Spiele auszuführen. Es wird empfoh<PERSON>, sie zu installieren, um Probleme zu vermeiden.", "install_common_redist": "Installieren", "installing_common_redist": "Installiere…", "show_download_speed_in_megabytes": "Download-Geschwindigkeit in Megabyte pro Sekunde anzeigen", "extract_files_by_default": "Dateien nach dem Download standardmäßig entpacken", "achievement_custom_notification_position": "Position der benutzerdefinierten Erfolgsbenachrichtigung", "top-left": "Oben links", "top-center": "<PERSON><PERSON> mittig", "top-right": "<PERSON><PERSON> rechts", "bottom-left": "Unten links", "bottom-center": "Unten mittig", "bottom-right": "Unten rechts", "enable_achievement_custom_notifications": "Benutzerdefinierte Erfolgsbenachrichtigungen aktivieren", "alignment": "Ausrichtung", "variation": "Variation", "default": "Standard", "rare": "Selten", "platinum": "<PERSON><PERSON><PERSON>", "hidden": "Versteckt", "test_notification": "Testbenachrichtigung", "notification_preview": "Vorschau der Erfolgsbenachrichtigung", "enable_friend_start_game_notifications": "Wenn ein Freund ein Spiel startet"}, "notifications": {"download_complete": "Download abgeschlossen", "game_ready_to_install": "{{title}} ist bereit zur Installation", "repack_list_updated": "Repack-Liste aktualisiert", "repack_count_one": "{{count}} <PERSON><PERSON> hinz<PERSON>", "repack_count_other": "{{count}} Repacks hinz<PERSON>fügt", "new_update_available": "Version {{version}} verfügbar", "restart_to_install_update": "Um das Update zu installieren, starte Hydra neu", "notification_achievement_unlocked_title": "<PERSON>rfolg für {{game}} freigeschaltet", "notification_achievement_unlocked_body": "{{achievement}} und {{count}} weitere wurden freigeschaltet", "new_friend_request_description": "{{displayName}} hat dir eine Freundschaftsanfrage gesendet", "new_friend_request_title": "Neue Freundschaftsanfrage", "extraction_complete": "Entpacken abgeschlossen", "game_extracted": "{{title}} erfolg<PERSON>ich entpackt", "friend_started_playing_game": "{{displayName}} hat begonnen, ein Spiel zu spielen", "test_achievement_notification_title": "Dies ist eine Testbenachrichtigung", "test_achievement_notification_description": "<PERSON><PERSON><PERSON><PERSON> cool, oder?"}, "system_tray": {"open": "<PERSON><PERSON><PERSON>", "quit": "Schließen"}, "game_card": {"available_one": "Verfügbar", "available_other": "Verfügbar", "no_downloads": "Keine Downloads verfügbar"}, "binary_not_found_modal": {"title": "Programme nicht installiert", "description": "Ausführbare Dateien für Wine oder Lutris wurden auf deinem System nicht gefunden", "instructions": "Überprüfe die korrekte Installation dieser für deine Linux-Distro, damit das Spiel normal laufen kann"}, "modal": {"close": "Knopf schließen"}, "forms": {"toggle_password_visibility": "Sichtbarkeit des Passworts umschalten"}, "user_profile": {"amount_hours": "{{amount}} St<PERSON><PERSON>", "amount_minutes": "{{amount}} Minuten", "last_time_played": "Zuletzt gespielt {{period}}", "activity": "Letzte Aktivität", "library": "Bibliothek", "total_play_time": "Gesamtspielzeit", "no_recent_activity_title": "Hmmm… hier ist nichts", "no_recent_activity_description": "Du hast in letzter Zeit keine Spiele gespielt. Es wird Zeit das zu ändern!", "display_name": "Anzeigename", "saving": "<PERSON><PERSON><PERSON><PERSON>", "save": "Speichern", "edit_profile": "<PERSON><PERSON>", "saved_successfully": "Erfolgreich gespeichert", "try_again": "Bitte versuche es erneut", "sign_out_modal_title": "Bist du dir sicher?", "cancel": "Abbrechen", "successfully_signed_out": "Erfolgreich abgemeldet", "sign_out": "Abmelden", "playing_for": "Spielt {{amount}} lang", "sign_out_modal_text": "Deine Bibliothek ist mit deinem aktuellen Konto verknüpft. Wenn du dich abmeldest, wird deine Bibliothek nicht mehr sichtbar sein und jeglicher Fortschritt wird nicht gespeichert. Abmelden fortführen?", "add_friends": "Freunde hinzufügen", "add": "Hinzufügen", "friend_code": "Freundescode", "see_profile": "<PERSON><PERSON> anzeigen", "sending": "Sendet", "friend_request_sent": "Freundschaftsanfrage versendet", "friends": "Freunde", "friends_list": "Freundesliste", "user_not_found": "<PERSON>utzer nicht gefunden", "block_user": "Nutzer blockieren", "add_friend": "Freund hinzufügen", "request_sent": "<PERSON><PERSON><PERSON>t", "request_received": "Anfrage erhalten", "accept_request": "<PERSON><PERSON><PERSON> an<PERSON>en", "ignore_request": "<PERSON><PERSON><PERSON> igno<PERSON>", "cancel_request": "Anfrage zur<PERSON>", "undo_friendship": "Freundschaft kündigen", "request_accepted": "<PERSON><PERSON><PERSON>", "user_blocked_successfully": "Nutzer erfolgreich blockiert", "user_block_modal_text": "{{displayName}} wird dadurch blockiert", "blocked_users": "<PERSON><PERSON><PERSON>", "unblock": "Freigeben", "no_friends_added": "Du hast noch keine Freunde hinzugefügt", "pending": "<PERSON><PERSON><PERSON><PERSON>", "no_pending_invites": "Du hast keine ausstehenden Einladungen", "no_blocked_users": "Du hast keine blockier<PERSON>", "friend_code_copied": "Freundescode kopiert", "undo_friendship_modal_text": "Freundschaft mit {{displayName}} wird dadurch gekündigt", "privacy_hint": "Um anzupassen, wer dies sehen kann, gehe zu den <0>Einstellungen</0>", "locked_profile": "Dieses Profil ist privat", "image_process_failure": "Fehler bei der Bildverarbeitung", "required_field": "<PERSON><PERSON> ist erford<PERSON>lich", "displayname_min_length": "Anzeigename muss mindestens 3 Zeichen lang sein", "displayname_max_length": "Anzeigename darf maximal 50 Zeichen lang sein", "report_profile": "<PERSON><PERSON>", "report_reason": "Warum meldest du dieses Profil?", "report_description": "Zusätzliche Informationen", "report_description_placeholder": "Zusätzliche Informationen", "report": "Melden", "report_reason_hate": "<PERSON><PERSON><PERSON><PERSON>", "report_reason_sexual_content": "<PERSON><PERSON><PERSON>", "report_reason_violence": "Gewalt", "report_reason_spam": "Spam", "report_reason_other": "Sonstiges", "profile_reported": "<PERSON><PERSON> g<PERSON>", "your_friend_code": "<PERSON><PERSON>code:", "upload_banner": "<PERSON> hochladen", "uploading_banner": "Banner wird hochgeladen…", "background_image_updated": "Hintergrundbild aktualisiert", "stats": "Statistiken", "achievements": "Erfolge", "games": "<PERSON><PERSON><PERSON>", "top_percentile": "Top {{percentile}}%", "ranking_updated_weekly": "Rangliste wird wöchentlich aktualisiert", "playing": "Spielt {{game}}", "achievements_unlocked": "Erfolge freigeschaltet", "earned_points": "<PERSON><PERSON>", "show_achievements_on_profile": "Zeige deine Erfolge auf deinem Profil", "show_points_on_profile": "Zeige deine verdienten Punkte auf deinem Profil"}, "achievement": {"achievement_unlocked": "Erfolg freigeschaltet", "user_achievements": "{{displayName}}'s Erfolge", "your_achievements": "<PERSON><PERSON>", "unlocked_at": "Freigeschaltet am: {{date}}", "subscription_needed": "Ein Hydra Cloud-Abonnement ist erforderlich, um diesen Inhalt zu sehen", "new_achievements_unlocked": "{{achievementCount}} neue <PERSON><PERSON><PERSON><PERSON> von {{gameCount}} <PERSON><PERSON><PERSON> freigeschaltet", "achievement_progress": "{{unlockedCount}}/{{totalCount}} Erfolge", "achievements_unlocked_for_game": "{{achievementCount}} neue Erfolge für {{gameTitle}} freigeschaltet", "hidden_achievement_tooltip": "Dies ist ein versteckter Erfolg", "achievement_earn_points": "Verdiene {{points}} Punkte mit diesem Erfolg", "earned_points": "<PERSON><PERSON>:", "available_points": "Verfügbare Punkte:", "how_to_earn_achievements_points": "Wie verdient man Erfolgspunkte?"}, "hydra_cloud": {"subscription_tour_title": "Hydra Cloud-Abonnement", "subscribe_now": "Jetzt abonnieren", "cloud_saving": "Cloud-S<PERSON><PERSON>rung", "cloud_achievements": "Speichere deine Erfolge in der Cloud", "animated_profile_picture": "Animierte Profilbilder", "premium_support": "Premium-Support", "show_and_compare_achievements": "Zeige und vergleiche deine Erfolge mit anderen Nutzern", "animated_profile_banner": "Animiertes Profilbanner", "hydra_cloud": "Hydra Cloud", "hydra_cloud_feature_found": "Du hast gerade eine Hydra Cloud-Funktion entdeckt!", "learn_more": "<PERSON><PERSON> er<PERSON>", "debrid_description": "Lade bis zu 4x schneller mit Nimbus herunter"}, "library": {"my_library": "Meine Bibliothek", "games": "<PERSON><PERSON><PERSON>", "search_library": "Bibliothek durchsuchen...", "search_games": "Spiele suchen...", "filters": "Filter", "sort_by": "Sortieren nach", "new_collection": "Neue Sammlung", "clear_all": "Alle Löschen", "clear_all_filters": "Alle Filter L<PERSON>", "status": "Status", "playable_only": "<PERSON><PERSON>", "genres": "Genres", "collections": "Sammlungen", "all_games": "Alle Spiele", "my_collections": "<PERSON><PERSON>", "smart_collections": "Intelligente Sammlungen", "no_collections": "<PERSON><PERSON>", "no_collections_yet": "Noch keine Sammlungen", "create_collection_hint": "<PERSON><PERSON><PERSON> deine erste Sammlung, um deine Spiele zu organisieren", "create_first_collection": "<PERSON><PERSON><PERSON> deine erste Sammlung, um deine Spiele zu organisieren", "edit_collection": "<PERSON><PERSON><PERSON>", "delete_collection": "Sammlung Löschen", "no_games_found": "<PERSON><PERSON> gefunden", "playable": "Spielbar", "not_installed": "<PERSON><PERSON>", "installed": "Installiert", "installed_only": "Nur installierte", "not_installed_only": "<PERSON>ur nicht installierte", "recently_played": "K<PERSON><PERSON>lich Gespielt", "favorites": "<PERSON><PERSON>", "not_played": "Nicht Gespielt", "last_played": "Zuletzt gespielt", "never": "<PERSON><PERSON>", "minutes": "Minuten", "hours": "Stunden", "hide_collections": "Sammlungen Ausblenden", "show_collections": "Sammlungen Anzeigen", "grid_view": "Rasteransicht", "list_view": "Listenansicht", "compact_cards": "Kompak<PERSON>", "normal_cards": "Normale Karten", "large_cards": "Große Karten", "suggestions": "Vorschläge", "results_found": "Ergebnisse gefunden", "clear_search": "Suche Löschen", "remove_filter": "<PERSON><PERSON>", "remove_genre": "<PERSON><PERSON> en<PERSON>", "reset_sort": "Sortierung zurücksetzen", "sorted_by": "Sort<PERSON><PERSON> nach", "search": "<PERSON><PERSON>", "close": "Schließen", "sort_by_name": "Name", "sort_by_date_added": "Hinzugefügt am", "sort_by_last_played": "Zuletzt Gespielt", "sort_by_playtime": "Spielzeit", "sort_by_status": "Status", "view_mode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card_size": "Kartengröße", "compact": "Kompakt", "normal": "Normal", "large": "<PERSON><PERSON><PERSON>", "game_actions": "Spiel-Aktionen", "details": "Details", "folder": "<PERSON><PERSON><PERSON>", "add": "Hinzufügen", "remove": "Entfernen", "more_actions": "Weitere Aktionen", "add_to_favorites": "Zu Favoriten Hinzufügen", "remove_from_favorites": "Aus Favoriten Entfernen", "go_to_game_page": "Zur Spielseite gehen", "add_to_collection": "Zur Sammlung hinzufügen", "collection_name_required": "Sammlungsname ist erforderlich", "collection_name_too_long": "Sammlungsname ist zu lang (maximal 50 Zeichen)", "collection_name_exists": "Eine Sammlung mit diesem Namen existiert bereits", "collection_save_failed": "Sammlung konnte nicht gespeichert werden", "collection_created": "Sammlung erfolgreich erstellt", "collection_updated": "Sammlung erfolgreich aktualisiert", "characters": "<PERSON><PERSON><PERSON>", "optional": "optional", "saving": "Speichern...", "save_changes": "Änderungen Speichern", "create_collection": "<PERSON><PERSON><PERSON>", "create_new_collection": "Neue Sammlung E<PERSON>n", "done": "<PERSON><PERSON><PERSON>", "loading_genres": "Genres werden geladen", "no_genres_available": "<PERSON><PERSON> verfügbar", "game": "Spiel", "playtime": "Spielzeit", "collection_name": "Sammlungsname", "enter_collection_name": "Sammlungsname eingeben", "description": "Beschreibung", "enter_collection_description": "Sammlungsbeschreibung eingeben", "collection_color": "Sammlungsfarbe", "preview": "Vorschau", "cancel": "Abbrechen", "empty_collection_title": "<PERSON>m<PERSON> ist leer", "empty_collection_subtitle": "<PERSON>ch keine Spiele in der Sammlung \"{{name}}\"", "empty_library_title": "Deine Bibliothek ist leer", "empty_library_subtitle": "Beginne mit dem Aufbau deiner Spielesammlung", "no_search_results_title": "<PERSON><PERSON> gefunden", "no_search_results_subtitle": "Versuche deine Suche für \"{{query}}\" anzupassen", "add_games": "Spiele Hinzufügen", "add_games_to_collection": "Spiele zur Sammlung Hinzufügen", "loading_library": "Bibliothek Laden", "loading_collections": "Lade deine Sammlungen...", "delete_collection_title": "Sammlung Löschen", "delete_collection_message": "Bist du sicher, dass du \"{{name}}\" löschen möchtest? Diese Aktion kann nicht rückgängig gemacht werden.", "delete_collection_warning": "Diese Aktion kann nicht rückgängig gemacht werden. Spiele werden nicht gelöscht, nur aus dieser Sammlung entfernt.", "collection_deleted_successfully": "Sammlung erfolgreich <PERSON>", "collection_delete_failed": "Sammlung konnte nicht gelöscht werden", "deleting": "Löschen...", "delete": "Löschen", "edit": "<PERSON><PERSON><PERSON>", "remove_from_collection": "Aus Sammlung Entfernen", "clear": "Löschen", "failed_to_load_collections": "Sammlungen konnten nicht geladen werden", "error_loading_library": "Fehler beim Laden der Bibliothek", "retry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "play": "<PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "more_options": "Weitere Optionen", "recently_added": "<PERSON><PERSON><PERSON><PERSON>", "most_played": "Am Meisten Gespielt", "showing": "Anzeigen", "active_filters": "Aktive Filter", "quick_filters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "empty_state_title": "Nichts anzuzeigen", "empty_state_subtitle": "Versuche deine Filter oder Suchkriterien anzupassen", "and_more_genres": "und {{count}} weitere", "open_folder": "Ordner öffnen", "remove_from_library": "Aus Bibliothek entfernen", "whats_new": "Was ist neu?", "loading_news": "Nachrichten laden...", "news_error": "Nachrichten konnten nicht geladen werden", "no_news_available": "<PERSON>ine Nachrichten für deine Spiele verfügbar", "refresh": "Aktualisieren", "news_just_now": "Gerade eben", "news_hours_ago": "Vor {{hours}}h", "news_yesterday": "Gestern", "news_days_ago": "Vor {{days}}d", "newsBy": "Von {{author}}", "openInBrowser": "<PERSON><PERSON>", "readFullArticle": "Vollständigen Artikel lesen", "publishedOn": "Veröffentlicht am {{date}}", "addBookmark": "Lesezeichen hinzufügen", "removeBookmark": "Lesezeichen entfernen", "share": "Teilen", "copyLink": "<PERSON>", "openInSteam": "In Steam öffnen", "event": "Event", "update": "Update", "announcement": "Ankündigung", "news": "Nachrichten", "genre_types": {"action": "Action", "adventure": "<PERSON><PERSON><PERSON>", "rpg": "RPG", "strategy": "Strategie", "simulation": "Simulation", "sports": "Sport", "racing": "<PERSON><PERSON>", "puzzle": "Puzzle", "platformer": "Jump'n'Run", "shooter": "Shooter", "fighting": "<PERSON><PERSON><PERSON>", "horror": "Horror", "survival": "Überleben", "indie": "Indie", "casual": "Gelegenheitsspiel", "western": "Western", "stealth": "Stealth", "mmorpg": "MMORPG", "roguelike": "Roguelike", "sandbox": "Sandbox", "tower defense": "Tower Defense", "visual novel": "Visual Novel", "card game": "Kartenspiel", "board game": "Brettspiel", "educational": "Lernspiel", "music": "Mu<PERSON>", "rhythm": "Rhythmus"}, "card_sizes": {"compact": "Kompakt", "normal": "Normal", "large": "<PERSON><PERSON><PERSON>"}, "view_modes": {"grid": "Rasteransicht", "list": "Listenansicht"}}}