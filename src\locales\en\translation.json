{"language_name": "English", "app": {"successfully_signed_in": "Successfully signed in"}, "home": {"featured": "Featured", "surprise_me": "Surprise me", "no_results": "No results found", "start_typing": "Starting typing to search...", "hot": "Hot now", "weekly": "📅 Top games of the week", "achievements": "🏆 Games to beat"}, "sidebar": {"catalogue": "Catalogue", "downloads": "Downloads", "library": "Library", "settings": "Settings", "my_library": "My library", "downloading_metadata": "{{title}} (Downloading metadata…)", "paused": "{{title}} (Paused)", "downloading": "{{title}} ({{percentage}} - Downloading…)", "filter": "Filter library", "home": "Home", "queued": "{{title}} (Queued)", "game_has_no_executable": "Game has no executable selected", "sign_in": "Sign in", "friends": "Friends", "need_help": "Need help?", "favorites": "Favorites", "playable_button_title": "Show only games you can play now"}, "header": {"search": "Search games", "home": "Home", "catalogue": "Catalogue", "downloads": "Downloads", "search_results": "Search results", "settings": "Settings", "version_available_install": "Version {{version}} available. Click here to restart and install.", "version_available_download": "Version {{version}} available. Click here to download."}, "bottom_panel": {"no_downloads_in_progress": "No downloads in progress", "downloading_metadata": "Downloading {{title}} metadata…", "downloading": "Downloading {{title}}… ({{percentage}} complete) - Completion {{eta}} - {{speed}}", "calculating_eta": "Downloading {{title}}… ({{percentage}} complete) - Calculating remaining time…", "checking_files": "Checking {{title}} files… ({{percentage}} complete)", "installing_common_redist": "{{log}}…", "installation_complete": "Installation complete", "installation_complete_message": "Common redistributables installed successfully"}, "catalogue": {"search": "Filter…", "developers": "Developers", "genres": "Genres", "tags": "Tags", "publishers": "Publishers", "download_sources": "Download sources", "result_count": "{{resultCount}} results", "filter_count": "{{filterCount}} available", "clear_filters": "Clear {{filterCount}} selected"}, "game_details": {"open_download_options": "Open download options", "download_options_zero": "No download option", "download_options_one": "{{count}} download option", "download_options_other": "{{count}} download options", "updated_at": "Updated {{updated_at}}", "install": "Install", "resume": "Resume", "pause": "Pause", "cancel": "Cancel", "remove": "Remove", "space_left_on_disk": "{{space}} left on disk", "eta": "Conclusion {{eta}}", "calculating_eta": "Calculating remaining time…", "downloading_metadata": "Downloading metadata…", "filter": "Filter repacks", "requirements": "System requirements", "minimum": "Minimum", "recommended": "Recommended", "paused": "Paused", "release_date": "Released on {{date}}", "publisher": "Published by {{publisher}}", "hours": "hours", "minutes": "minutes", "amount_hours": "{{amount}} hours", "amount_minutes": "{{amount}} minutes", "accuracy": "{{accuracy}}% accuracy", "add_to_library": "Add to library", "remove_from_library": "Remove from library", "no_downloads": "No downloads available", "play_time": "Played for {{amount}}", "last_time_played": "Last played {{period}}", "not_played_yet": "You haven't played {{title}} yet", "next_suggestion": "Next suggestion", "play": "Play", "deleting": "Deleting installer…", "close": "Close", "playing_now": "Playing now", "change": "Change", "repacks_modal_description": "Choose the repack you want to download", "select_folder_hint": "To change the default folder, go to the <0>Settings</0>", "download_now": "Download now", "no_shop_details": "Could not retrieve shop details.", "download_options": "Download options", "download_path": "Download path", "previous_screenshot": "Previous screenshot", "next_screenshot": "Next screenshot", "screenshot": "Screenshot {{number}}", "open_screenshot": "Open screenshot {{number}}", "download_settings": "Download settings", "downloader": "Downloader", "select_executable": "Select", "no_executable_selected": "No executable selected", "open_folder": "Open folder", "open_download_location": "See downloaded files", "create_shortcut": "Create desktop shortcut", "clear": "Clear", "remove_files": "Remove files", "remove_from_library_title": "Are you sure?", "remove_from_library_description": "This will remove {{game}} from your library", "options": "Options", "executable_section_title": "Executable", "executable_section_description": "Path of the file that will be executed when \"Play\" is clicked", "downloads_section_title": "Downloads", "downloads_section_description": "Check out updates or other versions of this game", "danger_zone_section_title": "Danger zone", "danger_zone_section_description": "Remove this game from your library or the files downloaded by Hydra", "download_in_progress": "Download in progress", "download_paused": "Download paused", "last_downloaded_option": "Last downloaded option", "create_steam_shortcut": "Create Steam shortcut", "create_shortcut_success": "Shortcut created successfully", "you_might_need_to_restart_steam": "You might need to restart Steam to see the changes", "create_shortcut_error": "Error creating shortcut", "nsfw_content_title": "This game contains inappropriate content", "nsfw_content_description": "{{title}} contains content that may not be suitable for all ages. Are you sure you want to continue?", "allow_nsfw_content": "Continue", "refuse_nsfw_content": "Go back", "stats": "Stats", "download_count": "Downloads", "player_count": "Active players", "download_error": "This download option is not available", "download": "Download", "executable_path_in_use": "Executable already in use by \"{{game}}\"", "warning": "Warning:", "hydra_needs_to_remain_open": "for this download, Hydra needs to remain open util it's completed. If Hydra closes before completing, you will lose your progress.", "achievements": "Achievements", "achievements_count": "Achievements {{unlockedCount}}/{{achievementsCount}}", "cloud_save": "Cloud save", "cloud_save_description": "Save your progress in the cloud and continue playing on any device", "backups": "Backups", "install_backup": "Install", "delete_backup": "Delete", "create_backup": "New backup", "last_backup_date": "Last backup on {{date}}", "no_backup_preview": "No save games were found for this title", "restoring_backup": "Restoring backup ({{progress}} complete)…", "uploading_backup": "Uploading backup…", "no_backups": "You haven't created any backups for this game yet", "backup_uploaded": "Backup uploaded", "backup_deleted": "Backup deleted", "backup_restored": "Backup restored", "see_all_achievements": "See all achievements", "sign_in_to_see_achievements": "Sign in to see achievements", "mapping_method_automatic": "Automatic", "mapping_method_manual": "Manual", "mapping_method_label": "Mapping method", "files_automatically_mapped": "Files automatically mapped", "no_backups_created": "No backups created for this game", "manage_files": "Manage files", "loading_save_preview": "Searching for save games…", "wine_prefix": "Wine Prefix", "wine_prefix_description": "The Wine prefix used to run this game", "launch_options": "Launch Options", "launch_options_description": "Advanced users may choose to enter modifications to their launch options (experimental feature)", "launch_options_placeholder": "No parameter specified", "no_download_option_info": "No information available", "backup_deletion_failed": "Failed to delete backup", "max_number_of_artifacts_reached": "Maximum number of backups reached for this game", "achievements_not_sync": "See how to synchronize your achievements", "manage_files_description": "Manage which files will be backed up and restored", "select_folder": "Select folder", "backup_from": "Backup from {{date}}", "automatic_backup_from": "Automatic backup from {{date}}", "enable_automatic_cloud_sync": "Enable automatic cloud sync", "custom_backup_location_set": "Custom backup location set", "no_directory_selected": "No directory selected", "no_write_permission": "Cannot download into this directory. Click here to learn more.", "reset_achievements": "Reset achievements", "reset_achievements_description": "This will reset all achievements for {{game}}", "reset_achievements_title": "Are you sure?", "reset_achievements_success": "Achievements successfully reset", "reset_achievements_error": "Failed to reset achievements", "download_error_gofile_quota_exceeded": "You have exceeded your Gofile monthly quota. Please await the quota to reset.", "download_error_real_debrid_account_not_authorized": "Your Real-Debrid account is not authorized to make new downloads. Please check your account settings and try again.", "download_error_not_cached_on_real_debrid": "This download is not available on Real-Debrid and polling download status from Real-Debrid is not yet available.", "download_error_not_cached_on_torbox": "This download is not available on TorBox and polling download status from TorBox is not yet available.", "download_error_not_cached_on_hydra": "This download is not available on Nimbus.", "game_removed_from_favorites": "Game removed from favorites", "game_added_to_favorites": "Game added to favorites", "automatically_extract_downloaded_files": "Automatically extract downloaded files", "create_start_menu_shortcut": "Create Start Menu shortcut", "invalid_wine_prefix_path": "Invalid Wine prefix path", "invalid_wine_prefix_path_description": "The path to the Wine prefix is invalid. Please check the path and try again.", "missing_wine_prefix": "Wine prefix is required to create a backup on Linux", "artifact_renamed": "Backup renamed successfully", "rename_artifact": "<PERSON><PERSON>", "rename_artifact_description": "Rename the backup to a more descriptive name", "artifact_name_label": "Backup name", "artifact_name_placeholder": "Enter a name for the backup", "save_changes": "Save changes", "required_field": "This field is required", "max_length_field": "This field must be less than {{length}} characters", "freeze_backup": "Pin it so it's not overwritten by automatic backups", "unfreeze_backup": "Unpin it", "backup_frozen": "Backup pinned", "backup_unfrozen": "Backup unpinned", "backup_freeze_failed": "Failed to freeze backup", "backup_freeze_failed_description": "You must leave at least one free slot for automatic backups"}, "activation": {"title": "Activate Hydra", "installation_id": "Installation ID:", "enter_activation_code": "Enter your activation code", "message": "If you don't know where to ask for this, then you shouldn't have this.", "activate": "Activate", "loading": "Loading…"}, "downloads": {"resume": "Resume", "pause": "Pause", "eta": "Conclusion {{eta}}", "paused": "Paused", "verifying": "Verifying…", "completed": "Completed", "removed": "Not downloaded", "cancel": "Cancel", "filter": "Filter downloaded games", "remove": "Remove", "downloading_metadata": "Downloading metadata…", "deleting": "Deleting installer…", "delete": "Remove installer", "delete_modal_title": "Are you sure?", "delete_modal_description": "This will remove all the installation files from your computer", "install": "Install", "download_in_progress": "In progress", "queued_downloads": "Queued downloads", "downloads_completed": "Completed", "queued": "Queued", "no_downloads_title": "Such empty", "no_downloads_description": "You haven't downloaded anything with Hydra yet, but it's never too late to start.", "checking_files": "Checking files…", "seeding": "Seeding", "stop_seeding": "Stop seeding", "resume_seeding": "Resume seeding", "options": "Manage", "extract": "Extract files", "extracting": "Extracting files…"}, "settings": {"downloads_path": "Downloads path", "change": "Update", "notifications": "Notifications", "enable_download_notifications": "When a download is complete", "enable_repack_list_notifications": "When a new repack is added", "real_debrid_api_token_label": "Real-Debrid API token", "quit_app_instead_hiding": "Don't hide Hydra when closing", "launch_with_system": "Launch Hydra on system start-up", "general": "General", "behavior": "Behavior", "download_sources": "Download sources", "language": "Language", "api_token": "API Token", "enable_real_debrid": "Enable Real-Debrid", "real_debrid_description": "Real-Debrid is an unrestricted downloader that allows you to quickly download files, only limited by your internet speed.", "debrid_invalid_token": "Invalid API token", "debrid_api_token_hint": "You can get your API token <0>here</0>", "real_debrid_free_account_error": "The account \"{{username}}\" is a free account. Please subscribe to Real-Debrid", "debrid_linked_message": "Account \"{{username}}\" linked", "save_changes": "Save changes", "changes_saved": "Changes successfully saved", "download_sources_description": "Hydra will fetch the download links from these sources. The source URL must be a direct link to a .json file containing the download links.", "validate_download_source": "Validate", "remove_download_source": "Remove", "add_download_source": "Add source", "download_count_zero": "No download options", "download_count_one": "{{countFormatted}} download option", "download_count_other": "{{countFormatted}} download options", "download_source_url": "Download source URL", "add_download_source_description": "Insert the URL of the .json file", "download_source_up_to_date": "Up-to-date", "download_source_errored": "Errored", "sync_download_sources": "Sync sources", "removed_download_source": "Download source removed", "removed_download_sources": "Download sources removed", "cancel_button_confirmation_delete_all_sources": "No", "confirm_button_confirmation_delete_all_sources": "Yes, delete everything", "title_confirmation_delete_all_sources": "Delete all download sources", "description_confirmation_delete_all_sources": "You will delete all download sources", "button_delete_all_sources": "Remove all", "added_download_source": "Added download source", "download_sources_synced": "All download sources are synced", "insert_valid_json_url": "Insert a valid JSON url", "found_download_option_zero": "No download option found", "found_download_option_one": "Found {{countFormatted}} download option", "found_download_option_other": "Found {{countFormatted}} download options", "import": "Import", "public": "Public", "private": "Private", "friends_only": "Friends only", "privacy": "Privacy", "profile_visibility": "Profile visibility", "profile_visibility_description": "Choose who can see your profile and library", "required_field": "This field is required", "source_already_exists": "This source has already been added", "must_be_valid_url": "The source must be a valid URL", "blocked_users": "Blocked users", "user_unblocked": "User has been unblocked", "enable_achievement_notifications": "When an achievement is unlocked", "launch_minimized": "Launch Hydra minimized", "disable_nsfw_alert": "Disable NSFW alert", "seed_after_download_complete": "Seed after download complete", "show_hidden_achievement_description": "Show hidden achievements description before unlocking them", "account": "Account", "no_users_blocked": "You have no blocked users", "subscription_active_until": "Your Hydra Cloud is active until {{date}}", "manage_subscription": "Manage subscription", "update_email": "Update email", "update_password": "Update password", "current_email": "Current email:", "no_email_account": "You have not set an email yet", "account_data_updated_successfully": "Account data updated successfully", "renew_subscription": "Renew Hydra Cloud", "subscription_expired_at": "Your subscription expired at {{date}}", "no_subscription": "<PERSON><PERSON> in the best possible way", "become_subscriber": "Be Hydra Cloud", "subscription_renew_cancelled": "Automatic renewal is disabled", "subscription_renews_on": "Your subscription renews on {{date}}", "bill_sent_until": "Your next bill will be sent until this day", "no_themes": "Seems like you don't have any themes yet, but no worries, click here to create your first masterpiece.", "editor_tab_code": "Code", "editor_tab_info": "Info", "editor_tab_save": "Save", "web_store": "Web store", "clear_themes": "Clear", "create_theme": "Create", "create_theme_modal_title": "Create custom theme", "create_theme_modal_description": "Create a new theme to customize <PERSON><PERSON><PERSON>'s appearance", "theme_name": "Name", "insert_theme_name": "Insert theme name", "set_theme": "Set theme", "unset_theme": "Unset theme", "delete_theme": "Delete theme", "edit_theme": "Edit theme", "delete_all_themes": "Delete all themes", "delete_all_themes_description": "This will delete all your custom themes", "delete_theme_description": "This will delete the theme {{theme}}", "cancel": "Cancel", "appearance": "Appearance", "enable_torbox": "Enable TorBox", "torbox_description": "TorBox is your premium seedbox service rivaling even the best servers on the market.", "torbox_account_linked": "TorBox account linked", "create_real_debrid_account": "Click here if you don't have a Real-Debrid account yet", "create_torbox_account": "Click here if you don't have a TorBox account yet", "real_debrid_account_linked": "Real-Debrid account linked", "name_min_length": "Theme name must be at least 3 characters long", "import_theme": "Import theme", "import_theme_description": "You will import {{theme}} from the theme store", "error_importing_theme": "Error importing theme", "theme_imported": "Theme imported successfully", "enable_friend_request_notifications": "When a friend request is received", "enable_auto_install": "Download updates automatically", "common_redist": "Common redistributables", "common_redist_description": "Common redistributables are required to run some games. Installing them is recommended to avoid issues.", "install_common_redist": "Install", "installing_common_redist": "Installing…", "show_download_speed_in_megabytes": "Show download speed in megabytes per second", "extract_files_by_default": "Extract files by default after download", "achievement_custom_notification_position": "Achievement custom notification position", "top-left": "Top left", "top-center": "Top center", "top-right": "Top right", "bottom-left": "Bottom left", "bottom-center": "Bottom center", "bottom-right": "Bottom right", "enable_achievement_custom_notifications": "Enable achievement custom notifications", "alignment": "Alignment", "variation": "Variation", "default": "<PERSON><PERSON><PERSON>", "rare": "Rare", "platinum": "Platinum", "hidden": "Hidden", "test_notification": "Test notification", "notification_preview": "Achievement Notification Preview", "enable_friend_start_game_notifications": "When a friend starts playing a game"}, "notifications": {"download_complete": "Download complete", "game_ready_to_install": "{{title}} is ready to install", "repack_list_updated": "Repack list updated", "repack_count_one": "{{count}} repack added", "repack_count_other": "{{count}} repacks added", "new_update_available": "Version {{version}} available", "restart_to_install_update": "<PERSON><PERSON> H<PERSON> to install the update", "notification_achievement_unlocked_title": "Achievement unlocked for {{game}}", "notification_achievement_unlocked_body": "{{achievement}} and other {{count}} were unlocked", "new_friend_request_description": "{{displayName}} sent you a friend request", "new_friend_request_title": "New friend request", "extraction_complete": "Extraction complete", "game_extracted": "{{title}} extracted successfully", "friend_started_playing_game": "{{displayName}} started playing a game", "test_achievement_notification_title": "This is a test notification", "test_achievement_notification_description": "Pretty cool, huh?"}, "system_tray": {"open": "Open Hydra", "quit": "Quit"}, "game_card": {"available_one": "Available", "available_other": "Available", "no_downloads": "No downloads available"}, "binary_not_found_modal": {"title": "Programs not installed", "description": "Wine or Lutris executables were not found on your system", "instructions": "Check the correct way to install any of them on your Linux distro so that the game can run normally"}, "modal": {"close": "Close button"}, "forms": {"toggle_password_visibility": "Toggle password visibility"}, "user_profile": {"amount_hours": "{{amount}} hours", "amount_minutes": "{{amount}} minutes", "last_time_played": "Last played {{period}}", "activity": "Recent Activity", "library": "Library", "total_play_time": "Total playtime", "no_recent_activity_title": "Hmmm… nothing here", "no_recent_activity_description": "You haven't played any games recently. It's time to change that!", "display_name": "Display name", "saving": "Saving", "save": "Save", "edit_profile": "Edit Profile", "saved_successfully": "Saved successfully", "try_again": "Please, try again", "sign_out_modal_title": "Are you sure?", "cancel": "Cancel", "successfully_signed_out": "Successfully signed out", "sign_out": "Sign out", "playing_for": "Playing for {{amount}}", "sign_out_modal_text": "Your library is linked with your current account. When signing out, your library will not be visible anymore, and any progress will not be saved. Continue with sign out?", "add_friends": "Add Friends", "add": "Add", "friend_code": "Friend code", "see_profile": "See profile", "sending": "Sending", "friend_request_sent": "Friend request sent", "friends": "Friends", "friends_list": "Friends list", "user_not_found": "User not found", "block_user": "Block user", "add_friend": "Add friend", "request_sent": "Request sent", "request_received": "Request received", "accept_request": "Accept request", "ignore_request": "Ignore request", "cancel_request": "Cancel request", "undo_friendship": "Undo friendship", "request_accepted": "Request accepted", "user_blocked_successfully": "User blocked successfully", "user_block_modal_text": "This will block {{displayName}}", "blocked_users": "Blocked users", "unblock": "Unblock", "no_friends_added": "You have no added friends", "pending": "Pending", "no_pending_invites": "You have no pending invites", "no_blocked_users": "You have no blocked users", "friend_code_copied": "Friend code copied", "undo_friendship_modal_text": "This will undo your friendship with {{displayName}}", "privacy_hint": "To adjust who can see this, go to the <0>Settings</0>", "locked_profile": "This profile is private", "image_process_failure": "Failure while processing the image", "required_field": "This field is required", "displayname_min_length": "Display name must be at least 3 characters long", "displayname_max_length": "Display name must be at most 50 characters long", "report_profile": "Report this profile", "report_reason": "Why are you reporting this profile?", "report_description": "Additional information", "report_description_placeholder": "Additional information", "report": "Report", "report_reason_hate": "Hate speech", "report_reason_sexual_content": "Sexual content", "report_reason_violence": "Violence", "report_reason_spam": "Spam", "report_reason_other": "Other", "profile_reported": "Profile reported", "your_friend_code": "Your friend code:", "upload_banner": "Upload banner", "uploading_banner": "Uploading banner…", "background_image_updated": "Background image updated", "stats": "Stats", "achievements": "achievements", "games": "Games", "top_percentile": "Top {{percentile}}%", "ranking_updated_weekly": "Ranking is updated weekly", "playing": "Playing {{game}}", "achievements_unlocked": "Achievements Unlocked", "earned_points": "Earned points", "show_achievements_on_profile": "Show your achievements on your profile", "show_points_on_profile": "Show your earned points on your profile", "error_adding_friend": "Could not send friend request. Please check friend code"}, "achievement": {"achievement_unlocked": "Achievement unlocked", "user_achievements": "{{displayName}}'s Achievements", "your_achievements": "Your Achievements", "unlocked_at": "Unlocked at: {{date}}", "subscription_needed": "A Hydra Cloud subscription is required to see this content", "new_achievements_unlocked": "Unlocked {{achievementCount}} new achievements from {{gameCount}} games", "achievement_progress": "{{unlockedCount}}/{{totalCount}} achievements", "achievements_unlocked_for_game": "Unlocked {{achievementCount}} new achievements for {{gameTitle}}", "hidden_achievement_tooltip": "This is a hidden achievement", "achievement_earn_points": "Earn {{points}} points with this achievement", "earned_points": "Earned points:", "available_points": "Available points:", "how_to_earn_achievements_points": "How to earn achievements points?"}, "hydra_cloud": {"subscription_tour_title": "Hydra Cloud Subscription", "subscribe_now": "Subscribe now", "cloud_saving": "Cloud saving", "cloud_achievements": "Save your achievements on the cloud", "animated_profile_picture": "Animated profile pictures", "premium_support": "Premium Support", "show_and_compare_achievements": "Show and compare your achievements to other users", "animated_profile_banner": "Animated profile banner", "hydra_cloud": "Hydra Cloud", "hydra_cloud_feature_found": "You've just discovered a Hydra Cloud feature!", "learn_more": "Learn More", "debrid_description": "Download up to 4x faster with Nimbus"}, "library": {"my_library": "My Library", "games": "games", "search_library": "Search library...", "search_games": "Search games...", "filters": "Filters", "sort_by": "Sort by", "new_collection": "New Collection", "clear_all": "Clear All", "clear_all_filters": "Clear All Filters", "status": "Status", "playable_only": "Playable Only", "genres": "Genres", "collections": "Collections", "all_games": "All Games", "my_collections": "My Collections", "smart_collections": "Smart Collections", "no_collections": "No Collections", "no_collections_yet": "No collections yet", "create_collection_hint": "Create your first collection to organize your games", "create_first_collection": "Create your first collection to organize your games", "edit_collection": "Edit Collection", "delete_collection": "Delete Collection", "no_games_found": "No games found", "playable": "Playable", "not_installed": "Not installed", "installed": "Installed", "installed_only": "Installed Only", "not_installed_only": "Not Installed Only", "recently_played": "Recently Played", "favorites": "Favorites", "not_played": "Not Played", "last_played": "Last played", "never": "Never", "minutes": "minutes", "hours": "hours", "hide_collections": "Hide Collections", "show_collections": "Show Collections", "grid_view": "Grid View", "list_view": "List View", "compact_cards": "Compact Cards", "normal_cards": "Normal Cards", "large_cards": "Large Cards", "suggestions": "Suggestions", "results_found": "results found", "clear_search": "Clear search", "remove_filter": "Remove filter", "remove_genre": "Remove genre", "reset_sort": "Reset sort", "sorted_by": "Sorted by", "search": "Search", "close": "Close", "sort_by_name": "Name", "sort_by_date_added": "Date Added", "sort_by_last_played": "Last Played", "sort_by_playtime": "Playtime", "sort_by_status": "Status", "sort_by_name_asc": "Name A-Z", "sort_by_name_desc": "Name Z-A", "sort_by_last_played_asc": "Last Played ↑", "sort_by_last_played_desc": "Last Played ↓", "sort_by_playtime_asc": "Playtime ↑", "sort_by_playtime_desc": "Playtime ↓", "sort_by_status_asc": "Status ↑", "sort_by_status_desc": "Status ↓", "view_mode": "View Mode", "card_size": "<PERSON>", "compact": "Compact", "normal": "Normal", "large": "Large", "game_actions": "Game Actions", "details": "Details", "folder": "Folder", "add": "Add", "remove": "Remove", "more_actions": "More Actions", "add_to_favorites": "Add to Favorites", "remove_from_favorites": "Remove from Favorites", "go_to_game_page": "Go to game page", "add_to_collection": "Add to Collection", "collection_name_required": "Collection name is required", "collection_name_too_long": "Collection name is too long (max 50 characters)", "collection_name_exists": "A collection with this name already exists", "collection_save_failed": "Failed to save collection", "collection_created": "Collection created successfully", "collection_updated": "Collection updated successfully", "characters": "characters", "optional": "optional", "saving": "Saving...", "save_changes": "Save Changes", "create_collection": "Create Collection", "create_new_collection": "Create New Collection", "done": "Done", "loading_genres": "Loading genres", "no_genres_available": "No genres available", "game": "Game", "playtime": "Playtime", "collection_name": "Collection Name", "enter_collection_name": "Enter collection name", "description": "Description", "enter_collection_description": "Enter collection description", "collection_color": "Collection Color", "preview": "Preview", "cancel": "Cancel", "empty_collection_title": "Collection is empty", "empty_collection_subtitle": "No games in \"{{name}}\" collection yet", "empty_library_title": "Your library is empty", "empty_library_subtitle": "Start building your game collection", "no_search_results_title": "No games found", "no_search_results_subtitle": "Try adjusting your search for \"{{query}}\"", "add_games": "Add Games", "add_games_to_collection": "Add Games to Collection", "loading_library": "Loading Library", "loading_collections": "Loading your collections...", "delete_collection_title": "Delete Collection", "delete_collection_message": "Are you sure you want to delete \"{{name}}\"? This action cannot be undone.", "delete_collection_warning": "This action cannot be undone. Games will not be deleted, only removed from this collection.", "collection_deleted_successfully": "Collection deleted successfully", "collection_delete_failed": "Failed to delete collection", "deleting": "Deleting...", "delete": "Delete", "edit": "Edit", "remove_from_collection": "Remove from Collection", "clear": "Clear", "failed_to_load_collections": "Failed to load collections", "error_loading_library": "Error loading library", "retry": "Retry", "play": "Play", "download": "Download", "more_options": "More options", "recently_added": "Recently Added", "most_played": "Most Played", "showing": "Showing", "active_filters": "Active Filters", "open_folder": "Open folder", "remove_from_library": "Remove from library", "and_more_genres": "and {{count}} more", "quick_filters": "Quick Filters", "empty_state_title": "Nothing to show", "empty_state_subtitle": "Try adjusting your filters or search criteria", "view_details": "View Details", "whats_new": "What's new?", "loading_news": "Loading news...", "news_error": "Failed to load news", "no_news_available": "No news available for your games", "refresh": "Refresh", "news_just_now": "Just now", "news_hours_ago": "{{hours}}h ago", "news_yesterday": "Yesterday", "news_days_ago": "{{days}}d ago", "newsBy": "By {{author}}", "openInBrowser": "Open in browser", "readFullArticle": "Read full article", "publishedOn": "Published on {{date}}", "addBookmark": "Add bookmark", "removeBookmark": "Remove bookmark", "share": "Share", "copyLink": "Copy link", "openInSteam": "Open in Steam", "event": "Event", "update": "Update", "announcement": "Announcement", "news": "News", "genre_types": {"action": "Action", "adventure": "Adventure", "rpg": "RPG", "strategy": "Strategy", "simulation": "Simulation", "sports": "Sports", "racing": "Racing", "puzzle": "Puzzle", "platformer": "Platformer", "shooter": "Shooter", "fighting": "Fighting", "horror": "Horror", "survival": "Survival", "indie": "Indie", "casual": "Casual", "western": "Western", "stealth": "Stealth", "mmorpg": "MMORPG", "roguelike": "Roguelike", "sandbox": "Sandbox", "tower defense": "Tower Defense", "visual novel": "Visual Novel", "card game": "Card Game", "board game": "Board Game", "educational": "Educational", "music": "Music", "rhythm": "Rhythm"}, "card_sizes": {"compact": "Compact", "normal": "Normal", "large": "Large"}, "view_modes": {"grid": "Grid View", "list": "List View"}}}