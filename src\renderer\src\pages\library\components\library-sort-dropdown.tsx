import { useState, useRef, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { ChevronDownIcon, SortAscIcon, SortDescIcon } from "@primer/octicons-react";

import type { LibrarySortBy } from "@types";
import { getSortOptions, getSortLabel } from "@renderer/utils/library-sort-utils";

import "./library-sort-dropdown.scss";

/**
 * LibrarySortDropdown Component
 *
 * Touch-optimized sort dropdown for library games.
 * Provides comprehensive sorting options with clear visual hierarchy.
 *
 * Touch Optimizations:
 * - Minimum 48px touch targets for all interactive elements
 * - Steam Deck specific sizing (52px+ targets)
 * - Touch-friendly dropdown menu with larger options
 * - Simplified interaction model for handheld devices
 * - Clear visual feedback for selected options
 *
 * Features:
 * - All available sort options with icons
 * - Clear indication of current sort direction
 * - Keyboard navigation support
 * - Click outside to close functionality
 * - Responsive layout for different screen sizes
 *
 * Performance Features:
 * - Memoized event handlers to prevent unnecessary re-renders
 * - Efficient dropdown state management
 * - Optimized click outside detection
 * - Smooth animations and transitions
 */

interface LibrarySortDropdownProps {
  value: LibrarySortBy;
  onChange: (sortBy: LibrarySortBy) => void;
  className?: string;
}

export function LibrarySortDropdown({ value, onChange, className }: LibrarySortDropdownProps) {
  const { t } = useTranslation("library");
  const [isOpen, setIsOpen] = useState(false);
  const [menuPosition, setMenuPosition] = useState<'left' | 'right'>('left');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const sortOptions = getSortOptions();
  const currentLabel = getSortLabel(value);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isOpen]);

  // Close dropdown on escape key
  useEffect(() => {
    function handleKeyDown(event: KeyboardEvent) {
      if (event.key === "Escape") {
        setIsOpen(false);
        buttonRef.current?.focus();
      }
    }

    if (isOpen) {
      document.addEventListener("keydown", handleKeyDown);
      return () => document.removeEventListener("keydown", handleKeyDown);
    }
  }, [isOpen]);

  const handleOptionClick = (sortBy: LibrarySortBy) => {
    onChange(sortBy);
    setIsOpen(false);
    buttonRef.current?.focus();
  };

  const getSortIcon = (sortBy: LibrarySortBy) => {
    if (sortBy.endsWith("-asc")) {
      return <SortAscIcon size={14} />;
    }
    return <SortDescIcon size={14} />;
  };

  return (
    <div className={`library-sort-dropdown ${className || ""}`} ref={dropdownRef}>
      <button
        ref={buttonRef}
        type="button"
        className="library-sort-dropdown__trigger"
        onClick={() => {
          if (!isOpen && buttonRef.current) {
            // Check if menu would go off-screen and adjust position
            const buttonRect = buttonRef.current.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const menuWidth = 250; // Approximate menu width

            if (buttonRect.left + menuWidth > viewportWidth) {
              setMenuPosition('right');
            } else {
              setMenuPosition('left');
            }
          }
          setIsOpen(!isOpen);
        }}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        aria-label={t("sort_by")}
      >
        <div className="library-sort-dropdown__trigger-content">
          {getSortIcon(value)}
          <span className="library-sort-dropdown__trigger-text">{currentLabel}</span>
          <ChevronDownIcon 
            size={16} 
            className={`library-sort-dropdown__trigger-arrow ${isOpen ? "library-sort-dropdown__trigger-arrow--open" : ""}`}
          />
        </div>
      </button>

      {isOpen && (
        <div
          className={`library-sort-dropdown__menu library-sort-dropdown__menu--${menuPosition}`}
          role="listbox"
          data-position={menuPosition}
        >
          <div className="library-sort-dropdown__menu-header">
            <span className="library-sort-dropdown__menu-title">{t("sort_by")}</span>
          </div>
          
          <div className="library-sort-dropdown__menu-content">
            {sortOptions.map((option) => (
              <button
                key={option.value}
                type="button"
                className={`library-sort-dropdown__option ${
                  option.value === value ? "library-sort-dropdown__option--selected" : ""
                }`}
                onClick={() => handleOptionClick(option.value)}
                role="option"
                aria-selected={option.value === value}
              >
                <div className="library-sort-dropdown__option-content">
                  {getSortIcon(option.value)}
                  <span className="library-sort-dropdown__option-text">{option.label}</span>
                </div>
                {option.value === value && (
                  <div className="library-sort-dropdown__option-check">
                    <div className="library-sort-dropdown__option-check-mark" />
                  </div>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
