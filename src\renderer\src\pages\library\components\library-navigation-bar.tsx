import { useState, useCallback, useRef, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useLibrary } from "@renderer/hooks";
import { generateGameGenresMap, COMMON_GENRES, countGamesByGenre } from "@renderer/utils/genre-utils";
import {
  SearchIcon,
  FilterIcon,
  SortAscIcon,
  AppsIcon,
  ListUnorderedIcon,
  SquareIcon,
  SquareFillIcon,
  StopIcon,
  ChevronDownIcon,
  XIcon,
  GearIcon,
  ThreeBarsIcon,
  SidebarExpandIcon,
  DownloadIcon,
  TagIcon,
  ZapIcon,
  LocationIcon,
  StarIcon,
  GraphIcon,
  TrophyIcon,
  RocketIcon,
  CircleIcon,
  ArrowUpIcon,
  ShieldIcon,
  FlameIcon,
  MoonIcon,
  HeartIcon,
  SmileyIcon,
} from "@primer/octicons-react";

import { Button } from "@renderer/components";
import { LibrarySearch } from "./library-search";
import { getSortOptions, getSortLabel } from "@renderer/utils/library-sort-utils";

import type { LibraryGame, LibraryViewMode, LibraryCardSize, LibraryFilters, LibrarySortBy } from "@types";

import "./library-navigation-bar.scss";

interface LibraryNavigationBarProps {
  games: LibraryGame[];
  searchQuery: string;
  sortBy: LibrarySortBy;
  viewMode: LibraryViewMode;
  cardSize: LibraryCardSize;
  filters: LibraryFilters;
  onSearchChange: (value: string) => void;
  onSortChange: (sortBy: LibrarySortBy) => void;
  onViewModeChange: (mode: LibraryViewMode) => void;
  onCardSizeChange: (size: LibraryCardSize) => void;
  onToggleCollectionsSidebar: () => void;
  onUpdateFilters: (filters: Partial<LibraryFilters>) => void;
  onClearFilters: () => void;
  collectionsVisible: boolean;
}

const getGenreIcon = (genre: string) => {
  const iconMap: Record<string, React.ComponentType<any>> = {
    'Action': ZapIcon,
    'Adventure': LocationIcon,
    'RPG': StarIcon,
    'Strategy': GraphIcon,
    'Simulation': GearIcon,
    'Sports': TrophyIcon,
    'Racing': RocketIcon,
    'Puzzle': CircleIcon,
    'Platformer': ArrowUpIcon,
    'Shooter': ShieldIcon,
    'Fighting': FlameIcon,
    'Horror': MoonIcon,
    'Survival': SquareIcon,
    'Indie': HeartIcon,
    'Casual': SmileyIcon
  };

  return iconMap[genre] || StarIcon;
};

const normalizeGenreName = (genre: string): string => {
  return genre.trim().replace(/[^\w\s]/g, '').replace(/\s+/g, ' ');
};

export function LibraryNavigationBar({
  games,
  searchQuery,
  sortBy,
  viewMode,
  cardSize,
  filters,
  onSearchChange,
  onSortChange,
  onViewModeChange,
  onCardSizeChange,
  onToggleCollectionsSidebar,
  onUpdateFilters,
  onClearFilters,
  collectionsVisible,
}: LibraryNavigationBarProps) {
  const { t } = useTranslation("library");
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [showSortDropdown, setShowSortDropdown] = useState(false);
  const [showFiltersDropdown, setShowFiltersDropdown] = useState(false);
  const [gameGenres, setGameGenres] = useState<Map<string, string[]>>(new Map());
  const [isLoadingGenres, setIsLoadingGenres] = useState(false);
  const sortDropdownRef = useRef<HTMLDivElement>(null);
  const filtersDropdownRef = useRef<HTMLDivElement>(null);
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  const { library } = useLibrary();

  // Load genres from library
  useEffect(() => {
    if (library.length === 0) return;

    setIsLoadingGenres(true);

    // Simulate loading for better UX
    setTimeout(() => {
      const genresMap = generateGameGenresMap(library);
      setGameGenres(genresMap);
      setIsLoadingGenres(false);
    }, 300);
  }, [library]);

  // Count games per genre using improved counting system
  const genreCounts = useMemo(() => {
    return countGamesByGenre(library);
  }, [library]);

  // Extract available genres from fetched data, plus show all common genres
  const availableGenres = useMemo(() => {
    const genresSet = new Set<string>(COMMON_GENRES);

    gameGenres.forEach((genres) => {
      genres.forEach((genre) => {
        if (genre && typeof genre === 'string') {
          // Normalize genre names to avoid duplicates
          const normalizedGenre = normalizeGenreName(genre);
          if (normalizedGenre) {
            genresSet.add(normalizedGenre);
          }
        }
      });
    });

    return Array.from(genresSet).sort();
  }, [gameGenres]);



  // Close dropdowns when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (sortDropdownRef.current && !sortDropdownRef.current.contains(event.target as Node)) {
        setShowSortDropdown(false);
      }
      if (filtersDropdownRef.current && !filtersDropdownRef.current.contains(event.target as Node)) {
        setShowFiltersDropdown(false);
      }
      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target as Node)) {
        setShowMobileMenu(false);
      }
    }

    if (showSortDropdown || showFiltersDropdown || showMobileMenu) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [showSortDropdown, showFiltersDropdown, showMobileMenu]);

  const handleSortClick = useCallback(() => {
    setShowSortDropdown(!showSortDropdown);
    setShowFiltersDropdown(false);
    setShowMobileMenu(false);
  }, [showSortDropdown]);

  const handleFiltersClick = useCallback(() => {
    setShowFiltersDropdown(!showFiltersDropdown);
    setShowSortDropdown(false);
    setShowMobileMenu(false);
  }, [showFiltersDropdown]);

  const handleSortSelect = useCallback((newSortBy: LibrarySortBy) => {
    onSortChange(newSortBy);
    setShowSortDropdown(false);
  }, [onSortChange]);

  const sortOptions = getSortOptions();

  const handleMobileMenuToggle = useCallback(() => {
    setShowMobileMenu(!showMobileMenu);
    setShowSortDropdown(false);
  }, [showMobileMenu]);

  const handleInstalledToggle = useCallback(() => {
    onUpdateFilters({
      showInstalledOnly: !filters.showInstalledOnly,
      showNotInstalledOnly: false,
    });
  }, [filters.showInstalledOnly, onUpdateFilters]);

  const handleNotInstalledToggle = useCallback(() => {
    onUpdateFilters({
      showNotInstalledOnly: !filters.showNotInstalledOnly,
      showInstalledOnly: false,
    });
  }, [filters.showNotInstalledOnly, onUpdateFilters]);

  const handleGenreToggle = useCallback((genre: string, checked: boolean) => {
    let newGenres: string[];

    if (checked) {
      // Add genre if not already present
      newGenres = filters.genres.includes(genre)
        ? filters.genres
        : [...filters.genres, genre];
    } else {
      // Remove genre
      newGenres = filters.genres.filter((g) => g !== genre);
    }

    onUpdateFilters({ genres: newGenres });
  }, [filters.genres, onUpdateFilters]);

  // Count active filters
  const activeFiltersCount = useMemo(() => {
    let count = 0;
    if (filters.showInstalledOnly) count++;
    if (filters.showNotInstalledOnly) count++;
    if (filters.genres.length > 0) count += filters.genres.length;
    return count;
  }, [filters.showInstalledOnly, filters.showNotInstalledOnly, filters.genres.length]);

  const getSortIcon = () => {
    return sortBy.includes("asc") ? <SortAscIcon size={20} /> : <SortAscIcon size={20} style={{ transform: "rotate(180deg)" }} />;
  };

  const currentSortLabel = getSortLabel(sortBy);

  return (
    <div className="library-navigation-bar">
      {/* Main Navigation - Touch Optimized */}
      <div className="library-navigation-bar__main">
        {/* Left Section - Collections & Search */}
        <div className="library-navigation-bar__left">
          {/* Collections Toggle - Large Touch Target */}
          <button
            type="button"
            onClick={onToggleCollectionsSidebar}
            className={`library-navigation-bar__collections-toggle ${
              collectionsVisible ? "library-navigation-bar__collections-toggle--active" : ""
            }`}
            title={collectionsVisible ? t("hide_collections") : t("show_collections")}
          >
            <SidebarExpandIcon size={24} />
            <span className="library-navigation-bar__collections-text">
              {t("collections")}
            </span>
          </button>

          {/* Search - Full Width on Mobile */}
          <div className="library-navigation-bar__search-container">
            <LibrarySearch
              value={searchQuery}
              onChange={onSearchChange}
              games={games}
              className="library-navigation-bar__search"
              placeholder={t("search_games")}
            />
          </div>


        </div>

        {/* Right Section - Controls */}
        <div className="library-navigation-bar__right">
          {/* Desktop Controls - Hidden on Mobile */}
          <div className="library-navigation-bar__desktop-controls">
            {/* Sort Control */}
            <div className="library-navigation-bar__sort" ref={sortDropdownRef}>
              <button
                type="button"
                onClick={handleSortClick}
                className={`library-navigation-bar__sort-button ${showSortDropdown ? "library-navigation-bar__sort-button--active" : ""}`}
              >
                {getSortIcon()}
                <span className="library-navigation-bar__sort-text">
                  {currentSortLabel}
                </span>
                <ChevronDownIcon size={16} className={`library-navigation-bar__sort-chevron ${showSortDropdown ? "library-navigation-bar__sort-chevron--open" : ""}`} />
              </button>

              {showSortDropdown && (
                <div className="library-navigation-bar__sort-dropdown">
                  <div className="library-navigation-bar__sort-dropdown-header">
                    <span className="library-navigation-bar__sort-dropdown-title">{t("sort_by")}</span>
                  </div>
                  <div className="library-navigation-bar__sort-dropdown-content">
                    {sortOptions.map((option) => (
                      <button
                        key={option.value}
                        type="button"
                        className={`library-navigation-bar__sort-option ${
                          option.value === sortBy ? "library-navigation-bar__sort-option--selected" : ""
                        }`}
                        onClick={() => handleSortSelect(option.value)}
                      >
                        <div className="library-navigation-bar__sort-option-content">
                          {option.value.includes("asc") ? (
                            <SortAscIcon size={14} />
                          ) : (
                            <SortAscIcon size={14} style={{ transform: "rotate(180deg)" }} />
                          )}
                          <span className="library-navigation-bar__sort-option-text">{option.label}</span>
                        </div>
                        {option.value === sortBy && (
                          <div className="library-navigation-bar__sort-option-check">
                            <div className="library-navigation-bar__sort-option-check-mark" />
                          </div>
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Smart Filters Dropdown */}
            <div className="library-navigation-bar__filters" ref={filtersDropdownRef}>
              <button
                type="button"
                onClick={handleFiltersClick}
                className={`library-navigation-bar__filters-button ${
                  showFiltersDropdown ? "library-navigation-bar__filters-button--active" : ""
                } ${activeFiltersCount > 0 ? "library-navigation-bar__filters-button--has-active" : ""}`}
              >
                <FilterIcon size={20} />
                <span className="library-navigation-bar__filters-text">
                  {t("filters")}
                  {activeFiltersCount > 0 && (
                    <span className="library-navigation-bar__filters-count">
                      {activeFiltersCount}
                    </span>
                  )}
                </span>
                <ChevronDownIcon
                  size={16}
                  className={`library-navigation-bar__filters-chevron ${showFiltersDropdown ? "library-navigation-bar__filters-chevron--open" : ""}`}
                />
              </button>

              {showFiltersDropdown && (
                <div className="library-navigation-bar__filters-dropdown">
                  <div className="library-navigation-bar__filters-dropdown-header">
                    <span className="library-navigation-bar__filters-dropdown-title">{t("filters")}</span>
                    {activeFiltersCount > 0 && (
                      <button
                        type="button"
                        className="library-navigation-bar__filters-clear-all"
                        onClick={onClearFilters}
                      >
                        <XIcon size={14} />
                        {t("clear_all")}
                      </button>
                    )}
                  </div>

                  <div className="library-navigation-bar__filters-dropdown-content">
                    {/* Status Filters */}
                    <div className="library-navigation-bar__filters-section">
                      <h4 className="library-navigation-bar__filters-section-title">
                        <GearIcon size={14} />
                        {t("status")}
                      </h4>
                      <div className="library-navigation-bar__filters-options">
                        <button
                          type="button"
                          className={`library-navigation-bar__filter-option ${
                            filters.showInstalledOnly ? "library-navigation-bar__filter-option--active" : ""
                          }`}
                          onClick={handleInstalledToggle}
                        >
                          <DownloadIcon size={16} />
                          <span>{t("installed_only")}</span>
                          {filters.showInstalledOnly && <div className="library-navigation-bar__filter-option-check" />}
                        </button>

                        <button
                          type="button"
                          className={`library-navigation-bar__filter-option ${
                            filters.showNotInstalledOnly ? "library-navigation-bar__filter-option--active" : ""
                          }`}
                          onClick={handleNotInstalledToggle}
                        >
                          <XIcon size={16} />
                          <span>{t("not_installed_only")}</span>
                          {filters.showNotInstalledOnly && <div className="library-navigation-bar__filter-option-check" />}
                        </button>
                      </div>
                    </div>

                    {/* Genres Selection */}
                    <div className="library-navigation-bar__filters-section">
                      <h4 className="library-navigation-bar__filters-section-title">
                        <TagIcon size={14} />
                        {t("genres")} {filters.genres.length > 0 && `(${filters.genres.length})`}
                      </h4>

                      <div className="library-navigation-bar__genres-grid">
                        {isLoadingGenres ? (
                          <div className="library-navigation-bar__genres-loading">
                            <div className="library-navigation-bar__loading-spinner"></div>
                            <span>{t("loading_genres")}...</span>
                          </div>
                        ) : availableGenres.length > 0 ? (
                          availableGenres.map((genre) => {
                            const IconComponent = getGenreIcon(genre);
                            const isSelected = filters.genres.includes(genre);
                            const count = genreCounts[genre] || 0;

                            return (
                              <button
                                key={genre}
                                type="button"
                                className={`library-navigation-bar__genre-chip ${
                                  isSelected ? 'library-navigation-bar__genre-chip--selected' : ''
                                }`}
                                onClick={() => handleGenreToggle(genre, !isSelected)}
                              >
                                <div className="library-navigation-bar__genre-chip-icon">
                                  <IconComponent size={14} />
                                </div>
                                <span className="library-navigation-bar__genre-chip-text">
                                  {t(`genre_types.${genre.toLowerCase()}`, genre)}
                                </span>
                                <span className="library-navigation-bar__genre-chip-count">
                                  {count}
                                </span>
                                {isSelected && (
                                  <div className="library-navigation-bar__genre-chip-check">
                                    <div className="library-navigation-bar__genre-chip-check-mark" />
                                  </div>
                                )}
                              </button>
                            );
                          })
                        ) : (
                          <div className="library-navigation-bar__no-genres">
                            <TagIcon size={20} />
                            <span>{t("no_genres_available")}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* View Mode Controls */}
            <div className="library-navigation-bar__view-controls">
              <button
                type="button"
                className={`library-navigation-bar__view-button ${
                  viewMode === "grid" ? "library-navigation-bar__view-button--active" : ""
                }`}
                onClick={() => onViewModeChange("grid")}
                title={t("grid_view")}
              >
                <AppsIcon size={20} />
              </button>
              <button
                type="button"
                className={`library-navigation-bar__view-button ${
                  viewMode === "list" ? "library-navigation-bar__view-button--active" : ""
                }`}
                onClick={() => onViewModeChange("list")}
                title={t("list_view")}
              >
                <ListUnorderedIcon size={20} />
              </button>
            </div>
          </div>

          {/* Mobile Menu Toggle */}
          <button
            type="button"
            onClick={handleMobileMenuToggle}
            className={`library-navigation-bar__mobile-menu-toggle ${
              showMobileMenu ? "library-navigation-bar__mobile-menu-toggle--active" : ""
            }`}
          >
            <ThreeBarsIcon size={24} />
          </button>
        </div>
      </div>

      {/* Mobile Menu Dropdown */}
      {showMobileMenu && (
        <div className="library-navigation-bar__mobile-menu" ref={mobileMenuRef}>
          <div className="library-navigation-bar__mobile-menu-content">
            {/* Sort */}
            <div className="library-navigation-bar__mobile-item">
              <button
                type="button"
                onClick={handleSortClick}
                className="library-navigation-bar__mobile-button"
              >
                {getSortIcon()}
                <span>{currentSortLabel}</span>
                <ChevronDownIcon size={16} />
              </button>
              {showSortDropdown && (
                <div className="library-navigation-bar__mobile-sort-dropdown">
                  {sortOptions.map((option) => (
                    <button
                      key={option.value}
                      type="button"
                      className={`library-navigation-bar__mobile-sort-option ${
                        option.value === sortBy ? "library-navigation-bar__mobile-sort-option--selected" : ""
                      }`}
                      onClick={() => handleSortSelect(option.value)}
                    >
                      <div className="library-navigation-bar__mobile-sort-option-content">
                        {option.value.includes("asc") ? (
                          <SortAscIcon size={16} />
                        ) : (
                          <SortAscIcon size={16} style={{ transform: "rotate(180deg)" }} />
                        )}
                        <span>{option.label}</span>
                      </div>
                      {option.value === sortBy && (
                        <div className="library-navigation-bar__mobile-sort-option-check">
                          <div className="library-navigation-bar__mobile-sort-option-check-mark" />
                        </div>
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Quick Filters */}
            <div className="library-navigation-bar__mobile-quick-filters">
              <span className="library-navigation-bar__mobile-label">{t("quick_filters")}</span>
              <div className="library-navigation-bar__mobile-filter-buttons">
                <button
                  type="button"
                  className={`library-navigation-bar__mobile-filter-button ${
                    filters.showInstalledOnly ? "library-navigation-bar__mobile-filter-button--active" : ""
                  }`}
                  onClick={handleInstalledToggle}
                >
                  <DownloadIcon size={16} />
                  <span>{t("installed")}</span>
                </button>

                <button
                  type="button"
                  className={`library-navigation-bar__mobile-filter-button ${
                    filters.showNotInstalledOnly ? "library-navigation-bar__mobile-filter-button--active" : ""
                  }`}
                  onClick={handleNotInstalledToggle}
                >
                  <XIcon size={16} />
                  <span>{t("not_installed")}</span>
                </button>
              </div>
            </div>



            {/* View Mode */}
            <div className="library-navigation-bar__mobile-view-controls">
              <span className="library-navigation-bar__mobile-label">{t("view_mode")}</span>
              <div className="library-navigation-bar__mobile-view-buttons">
                <button
                  type="button"
                  className={`library-navigation-bar__mobile-view-button ${
                    viewMode === "grid" ? "library-navigation-bar__mobile-view-button--active" : ""
                  }`}
                  onClick={() => onViewModeChange("grid")}
                >
                  <AppsIcon size={20} />
                  <span>{t("grid_view")}</span>
                </button>
                <button
                  type="button"
                  className={`library-navigation-bar__mobile-view-button ${
                    viewMode === "list" ? "library-navigation-bar__mobile-view-button--active" : ""
                  }`}
                  onClick={() => onViewModeChange("list")}
                >
                  <ListUnorderedIcon size={20} />
                  <span>{t("list_view")}</span>
                </button>
              </div>
            </div>

            {/* Card Size (only for grid view) */}
            {viewMode === "grid" && (
              <div className="library-navigation-bar__mobile-card-size">
                <span className="library-navigation-bar__mobile-label">{t("card_size")}</span>
                <div className="library-navigation-bar__mobile-card-buttons">
                  <button
                    type="button"
                    className={`library-navigation-bar__mobile-card-button ${
                      cardSize === "compact" ? "library-navigation-bar__mobile-card-button--active" : ""
                    }`}
                    onClick={() => onCardSizeChange("compact")}
                  >
                    <SquareIcon size={16} />
                    <span>{t("compact")}</span>
                  </button>
                  <button
                    type="button"
                    className={`library-navigation-bar__mobile-card-button ${
                      cardSize === "normal" ? "library-navigation-bar__mobile-card-button--active" : ""
                    }`}
                    onClick={() => onCardSizeChange("normal")}
                  >
                    <SquareFillIcon size={18} />
                    <span>{t("normal")}</span>
                  </button>
                  <button
                    type="button"
                    className={`library-navigation-bar__mobile-card-button ${
                      cardSize === "large" ? "library-navigation-bar__mobile-card-button--active" : ""
                    }`}
                    onClick={() => onCardSizeChange("large")}
                  >
                    <StopIcon size={20} />
                    <span>{t("large")}</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
