import type { LibraryGame, LibrarySortBy } from "@types";
import { getTimestamp } from "./date-utils";

/**
 * Sorts library games based on the specified sort criteria
 */
export function sortLibraryGames(games: LibraryGame[], sortBy: LibrarySortBy): LibraryGame[] {
  const sortedGames = [...games];

  switch (sortBy) {
    case "name-asc":
      return sortedGames.sort((a, b) => a.title.localeCompare(b.title));
    
    case "name-desc":
      return sortedGames.sort((a, b) => b.title.localeCompare(a.title));

    case "last-played-asc":
      return sortedGames.sort((a, b) => {
        const aTime = getTimestamp(a.lastTimePlayed) || 0;
        const bTime = getTimestamp(b.lastTimePlayed) || 0;
        return aTime - bTime;
      });

    case "last-played-desc":
      return sortedGames.sort((a, b) => {
        const aTime = getTimestamp(a.lastTimePlayed) || 0;
        const bTime = getTimestamp(b.lastTimePlayed) || 0;
        return bTime - aTime;
      });
    
    case "playtime-asc":
      return sortedGames.sort((a, b) => {
        const aTime = a.playTimeInMilliseconds || 0;
        const bTime = b.playTimeInMilliseconds || 0;
        return aTime - bTime;
      });
    
    case "playtime-desc":
      return sortedGames.sort((a, b) => {
        const aTime = a.playTimeInMilliseconds || 0;
        const bTime = b.playTimeInMilliseconds || 0;
        return bTime - aTime;
      });
    
    case "status-asc":
      return sortedGames.sort((a, b) => {
        const aInstalled = Boolean(a.executablePath);
        const bInstalled = Boolean(b.executablePath);
        // Not installed first, then installed
        return Number(aInstalled) - Number(bInstalled);
      });
    
    case "status-desc":
      return sortedGames.sort((a, b) => {
        const aInstalled = Boolean(a.executablePath);
        const bInstalled = Boolean(b.executablePath);
        // Installed first, then not installed
        return Number(bInstalled) - Number(aInstalled);
      });
    
    default:
      return sortedGames;
  }
}

/**
 * Get translation key for sort option
 */
export function getSortTranslationKey(sortBy: LibrarySortBy): string {
  const sortKeys: Record<LibrarySortBy, string> = {
    "name-asc": "sort_by_name",
    "name-desc": "sort_by_name",
    "last-played-asc": "sort_by_last_played",
    "last-played-desc": "sort_by_last_played",
    "playtime-asc": "sort_by_playtime",
    "playtime-desc": "sort_by_playtime",
    "status-asc": "sort_by_status",
    "status-desc": "sort_by_status",
  };

  return sortKeys[sortBy] || "sort_by_name";
}

/**
 * Get display label for sort option (deprecated - use getSortTranslationKey instead)
 * @deprecated Use getSortTranslationKey and translate in component
 */
export function getSortLabel(sortBy: LibrarySortBy): string {
  const sortLabels: Record<LibrarySortBy, string> = {
    "name-asc": "Name (A-Z)",
    "name-desc": "Name (Z-A)",
    "last-played-asc": "Last Played (Oldest)",
    "last-played-desc": "Last Played (Recent)",
    "playtime-asc": "Play Time (Least)",
    "playtime-desc": "Play Time (Most)",
    "status-asc": "Status (Not Installed)",
    "status-desc": "Status (Installed)",
  };

  return sortLabels[sortBy] || "Name (A-Z)";
}

/**
 * Get all available sort options with translation keys
 */
export function getSortOptionsWithTranslationKeys(): Array<{ value: LibrarySortBy; translationKey: string }> {
  const sortOptions: LibrarySortBy[] = [
    "name-asc",
    "name-desc",
    "last-played-desc",
    "last-played-asc",
    "playtime-desc",
    "playtime-asc",
    "status-desc",
    "status-asc",
  ];

  return sortOptions.map(value => ({
    value,
    translationKey: getSortTranslationKey(value),
  }));
}

/**
 * Get all available sort options (deprecated - use getSortOptionsWithTranslationKeys instead)
 * @deprecated Use getSortOptionsWithTranslationKeys and translate in component
 */
export function getSortOptions(): Array<{ value: LibrarySortBy; label: string }> {
  const sortOptions: LibrarySortBy[] = [
    "name-asc",
    "name-desc",
    "last-played-desc",
    "last-played-asc",
    "playtime-desc",
    "playtime-asc",
    "status-desc",
    "status-asc",
  ];

  return sortOptions.map(value => ({
    value,
    label: getSortLabel(value),
  }));
}
